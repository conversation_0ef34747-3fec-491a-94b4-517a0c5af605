package net.iofun.mdis.module.product.controller.admin.componentspu.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.iofun.mdis.framework.common.vo.BaseVO;
import net.iofun.mdis.framework.excel.core.annotations.DictFormat;
import net.iofun.mdis.framework.excel.core.convert.DictConvert;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 配件SPU精简 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ComponentSpuSimpleRespVO extends BaseVO {

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "分类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分类ID")
    private String categoryId;

    @Schema(description = "计量单位ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("计量单位ID")
    private String unitId;

}
