package net.iofun.mdis.module.product.controller.admin.componentsku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 配件SKU列表 Request VO")
@Data
public class ComponentSkuListReqVO {

    @Schema(description = "配件SPU-ID")
    private String superId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "标签中文")
    private String labelCn;

    @Schema(description = "标签英文")
    private String labelEn;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "开启状态")
    private Integer status;
}
