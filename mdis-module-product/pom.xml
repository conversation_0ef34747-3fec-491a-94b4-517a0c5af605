<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>net.iofun</groupId>
        <artifactId>mdis</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>mdis-module-product</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        product 模块, 包括产品分类, 属性, 单位, 品牌, 配件, 产品图片, 产品评论, 产品系数
    </description>

    <dependencies>

        <!-- DB 相关 -->
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-mongo</artifactId>
        </dependency>

        <!-- AviatorScript 计算框架 -->
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-aviatorscript</artifactId>
        </dependency>

        <!-- QLExpress4 嵌入式Java动态脚本工具 -->
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-qlexpress</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>