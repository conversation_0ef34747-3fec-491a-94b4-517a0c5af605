package net.iofun.mdis.module.infra.dal.repository.logger;

import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.controller.admin.logger.vo.apierrorlog.ApiErrorLogPageReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.logger.ApiErrorLogDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Repository
public class ApiErrorLogRepository extends BaseRepository<ApiErrorLogDO, ObjectId> {
    public ApiErrorLogRepository(MongoTemplate mongoOperations) {
        super(ApiErrorLogDO.class, mongoOperations);
    }

    public PageResult<ApiErrorLogDO> selectPage(ApiErrorLogPageReqVO reqVO) {
        LambdaQueryWrapperMongo<ApiErrorLogDO> queryWrapper = new LambdaQueryWrapperMongo<>(ApiErrorLogDO.class)
                .eqIfPresent(ApiErrorLogDO::getUserId, reqVO.getUserId())
                .eqIfPresent(ApiErrorLogDO::getUserType, reqVO.getUserType())
                .eqIfPresent(ApiErrorLogDO::getApplicationName, reqVO.getApplicationName())
                .likeIfPresent(ApiErrorLogDO::getRequestUrl, reqVO.getRequestUrl())
                .betweenIfPresent(ApiErrorLogDO::getExceptionTime, reqVO.getExceptionTime())
                .eqIfPresent(ApiErrorLogDO::getProcessStatus, reqVO.getProcessStatus())
                .orderByDesc(ApiErrorLogDO::getId);
        return selectPage(reqVO, queryWrapper);
    }

    /**
     * 物理删除指定时间之前的日志
     *
     * @param createTime 最大时间
     * @param limit 删除条数，防止一次删除太多
     * @return 删除条数
     */
    public Integer deleteByCreateTimeLt(LocalDateTime createTime, Integer limit) {
        LambdaQueryWrapperMongo<ApiErrorLogDO> queryWrapper = new LambdaQueryWrapperMongo<>(ApiErrorLogDO.class)
                .lteIfPresent(ApiErrorLogDO::getCreateTime, createTime)
                .limitIfPresent(limit);
        return (int)deleteByQuery(queryWrapper).getDeletedCount();
    }

    public ApiErrorLogDO selectById(String strId) {
        return selectById(new LambdaQueryWrapperMongo<>(ApiErrorLogDO.class)
                .eq(ApiErrorLogDO::getId, strId)).orElse(null);
    }
}
