package net.iofun.mdis.module.infra.service.demo.demo03;

import net.iofun.mdis.framework.common.pojo.PageParam;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.util.object.BeanUtils;
import net.iofun.mdis.module.infra.controller.admin.demo.demo03.vo.Demo03StudentPageReqVO;
import net.iofun.mdis.module.infra.controller.admin.demo.demo03.vo.Demo03StudentSaveReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.demo.demo03.Demo03CourseDO;
import net.iofun.mdis.module.infra.dal.dataobject.demo.demo03.Demo03GradeDO;
import net.iofun.mdis.module.infra.dal.dataobject.demo.demo03.Demo03StudentDO;
import net.iofun.mdis.module.infra.dal.repository.demo.demo03.Demo03CourseRepository;
import net.iofun.mdis.module.infra.dal.repository.demo.demo03.Demo03GradeRepository;
import net.iofun.mdis.module.infra.dal.repository.demo.demo03.Demo03StudentRepository;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static net.iofun.mdis.module.infra.enums.ErrorCodeConstants.*;

/**
 * 学生 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class Demo03StudentServiceImpl implements Demo03StudentService {

    @Resource
    private Demo03StudentRepository demo03StudentRepository;
    @Resource
    private Demo03CourseRepository demo03CourseRepository;
    @Resource
    private Demo03GradeRepository demo03GradeRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createDemo03Student(Demo03StudentSaveReqVO createReqVO) {
        // 插入
        Demo03StudentDO demo03Student = BeanUtils.toBean(createReqVO, Demo03StudentDO.class);
        demo03StudentRepository.insert(demo03Student, getLoginUser());

        // 插入子表
        createDemo03CourseList(demo03Student.getId().toHexString(), createReqVO.getDemo03Courses());
        createDemo03Grade(demo03Student.getId().toHexString(), createReqVO.getDemo03Grade());
        // 返回
        return demo03Student.getId().toHexString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDemo03Student(Demo03StudentSaveReqVO updateReqVO) {
        // 校验存在
        validateDemo03StudentExists(updateReqVO.getId());
        // 更新
        Demo03StudentDO updateObj = BeanUtils.toBean(updateReqVO, Demo03StudentDO.class);
        demo03StudentRepository.updateById(updateObj, getLoginUser());

        // 更新子表
        updateDemo03CourseList(updateReqVO.getId().toHexString(), updateReqVO.getDemo03Courses());
        updateDemo03Grade(updateReqVO.getId().toHexString(), updateReqVO.getDemo03Grade());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDemo03Student(String id) {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        validateDemo03StudentExists(oid);
        // 删除
        demo03StudentRepository.deleteById(oid, getLoginUser());

        // 删除子表
        deleteDemo03CourseByStudentId(id);
        deleteDemo03GradeByStudentId(id);
    }

    private void validateDemo03StudentExists(ObjectId id) {
        if (!demo03StudentRepository.validateExists(id)) {
            throw exception(DEMO03_STUDENT_NOT_EXISTS);
        }
    }

    @Override
    public Demo03StudentDO getDemo03Student(String id) {
        return demo03StudentRepository.selectById(id);
    }

    @Override
    public PageResult<Demo03StudentDO> getDemo03StudentPage(Demo03StudentPageReqVO pageReqVO) {
        return demo03StudentRepository.selectPage(pageReqVO);
    }

    // ==================== 子表（学生课程） ====================

    @Override
    public List<Demo03CourseDO> getDemo03CourseListByStudentId(String studentId) {
        return demo03CourseRepository.selectListByStudentId(studentId);
    }

    private void createDemo03CourseList(String studentId, List<Demo03CourseDO> list) {
        if (list != null) {
            list.forEach(o -> o.setStudentId(studentId));
        }
        demo03CourseRepository.insertBatch(list, getLoginUser());
    }

    private void updateDemo03CourseList(String studentId, List<Demo03CourseDO> list) {
//        deleteDemo03CourseByStudentId(studentId);
//		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
//        createDemo03CourseList(studentId, list);

        list.forEach(o ->
                demo03CourseRepository.upsertDemo03Course(studentId, o, getLoginUser())
        );



    }

    private void deleteDemo03CourseByStudentId(String studentId) {
        demo03CourseRepository.deleteByStudentId(studentId, getLoginUser());
    }

    @Override
    public PageResult<Demo03CourseDO> getDemo03CoursePage(PageParam pageReqVO, String studentId) {
        return demo03CourseRepository.selectPage(pageReqVO, studentId);
    }

    @Override
    public String createDemo03Course(Demo03CourseDO demo03Course) {
        demo03CourseRepository.insert(demo03Course, getLoginUser());
        return demo03Course.getId().toString();
    }

    @Override
    public void updateDemo03Course(Demo03CourseDO demo03Course) {
        demo03CourseRepository.updateById(demo03Course, getLoginUser());
    }

    @Override
    public void deleteDemo03Course(String id) {
        demo03CourseRepository.deleteById(new ObjectId(id), getLoginUser());
    }

    @Override
    public Demo03CourseDO getDemo03Course(String id) {
        return demo03CourseRepository.selectById(id);
    }

    // ==================== 子表（学生班级） ====================

    @Override
    public Demo03GradeDO getDemo03GradeByStudentId(String studentId) {
        return demo03GradeRepository.selectByStudentId(studentId);
    }

    private void createDemo03Grade(String studentId, Demo03GradeDO demo03Grade) {
        if (demo03Grade == null) {
            return;
        }
        demo03Grade.setStudentId(studentId);
        demo03GradeRepository.insert(demo03Grade, getLoginUser());
    }

    private void updateDemo03Grade(String studentId, Demo03GradeDO demo03Grade) {
        if (demo03Grade == null) {
			return;
        }
        demo03Grade.setStudentId(studentId);
//        demo03Grade.setUpdater(null).setUpdateTime(null); // 解决更新情况下：updateTime 不更新
        demo03GradeRepository.insertOrUpdate(demo03Grade, getLoginUser());
    }

    private void deleteDemo03GradeByStudentId(String studentId) {
        demo03GradeRepository.deleteByStudentId(studentId, getLoginUser());
    }

    @Override
    public PageResult<Demo03GradeDO> getDemo03GradePage(PageParam pageReqVO, String studentId) {
        return demo03GradeRepository.selectPage(pageReqVO, studentId);
    }

    @Override
    public String createDemo03Grade(Demo03GradeDO demo03Grade) {
        // 校验是否已经存在
        if (demo03GradeRepository.selectByStudentId(demo03Grade.getStudentId()) != null) {
            throw exception(DEMO03_GRADE_EXISTS);
        }
        demo03GradeRepository.insert(demo03Grade, getLoginUser());
        return demo03Grade.getId().toHexString();
    }

    @Override
    public void updateDemo03Grade(Demo03GradeDO demo03Grade) {
        // 校验存在
        validateDemo03GradeExists(demo03Grade.getId());
        // 更新
        demo03GradeRepository.updateById(demo03Grade, getLoginUser());
    }

    @Override
    public void deleteDemo03Grade(String id) {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        validateDemo03GradeExists(oid);
        // 删除
        demo03GradeRepository.deleteById(oid, getLoginUser());
    }

    @Override
    public Demo03GradeDO getDemo03Grade(String id) {
        return demo03GradeRepository.selectById(id);
    }

    private void validateDemo03GradeExists(ObjectId id) {
        if (!demo03GradeRepository.validateExists(id)) {
            throw exception(DEMO03_GRADE_NOT_EXISTS);
        }
    }

}