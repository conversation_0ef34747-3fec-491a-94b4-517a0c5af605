package net.iofun.mdis.module.infra.dal.dataobject.job;

import net.iofun.mdis.framework.common.entity.BaseEntity;
import net.iofun.mdis.module.infra.enums.job.JobStatusEnum;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 定时任务 DO
 *
 * <AUTHOR>
 */
@Document("infra_job")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobDO extends BaseEntity {

    /**
     * 任务编号
     */
//    @TableId
//    private Long id;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 任务状态
     *
     * 枚举 {@link JobStatusEnum}
     */
    private Integer status;
    /**
     * 处理器的名字
     */
    @Field("handler_name")
    private String handlerName;
    /**
     * 处理器的参数
     */
    @Field("handler_param")
    private String handlerParam;
    /**
     * CRON 表达式
     */
    @Field("cron_expression")
    private String cronExpression;

    // ========== 重试相关字段 ==========
    /**
     * 重试次数
     * 如果不重试，则设置为 0
     */
    @Field("retry_count")
    private Integer retryCount;
    /**
     * 重试间隔，单位：毫秒
     * 如果没有间隔，则设置为 0
     */
    @Field("retry_interval")
    private Integer retryInterval;

    // ========== 监控相关字段 ==========
    /**
     * 监控超时时间，单位：毫秒
     * 为空时，表示不监控
     *
     * 注意，这里的超时的目的，不是进行任务的取消，而是告警任务的执行时间过长
     */
    @Field("monitor_timeout")
    private Integer monitorTimeout;

}
