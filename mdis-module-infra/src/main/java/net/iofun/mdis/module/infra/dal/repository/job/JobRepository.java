package net.iofun.mdis.module.infra.dal.repository.job;

import com.mongodb.client.result.UpdateResult;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.controller.admin.job.vo.job.JobPageReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.job.JobDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class JobRepository extends BaseRepository<JobDO, ObjectId> {
    public JobRepository(MongoTemplate mongoOperations) {
        super(JobDO.class, mongoOperations);
    }

    public JobDO selectByHandlerName(String handlerName) {
        LambdaQueryWrapperMongo<JobDO> queryWrapper = new LambdaQueryWrapperMongo<>(JobDO.class)
                .eqIfPresent(JobDO::getHandlerName, handlerName);
        return findOne(queryWrapper).orElse(null);
    }

    public PageResult<JobDO> selectPage(JobPageReqVO reqVO) {
        LambdaQueryWrapperMongo<JobDO> queryWrapper = new LambdaQueryWrapperMongo<>(JobDO.class)
                .likeIfPresent(JobDO::getName, reqVO.getName())
                .eqIfPresent(JobDO::getStatus, reqVO.getStatus())
                .likeIfPresent(JobDO::getHandlerName, reqVO.getHandlerName());
        return selectPage(reqVO, queryWrapper);
    }

    public List<JobDO> selectList() {
        return selectList(new LambdaQueryWrapperMongo<>(JobDO.class));
    }
    
    public JobDO selectById(String strId) {
        return selectById(new LambdaQueryWrapperMongo<>(JobDO.class)
                .eq(JobDO::getId, strId)).orElse(null);
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        return softDeleteByQuery(new LambdaQueryWrapperMongo<>(JobDO.class)
                .eq(JobDO::getId, id), loginUser);
    }
}
