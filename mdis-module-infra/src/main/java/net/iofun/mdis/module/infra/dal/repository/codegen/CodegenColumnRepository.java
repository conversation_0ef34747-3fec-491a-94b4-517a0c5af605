package net.iofun.mdis.module.infra.dal.repository.codegen;

import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.CodegenColumnDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Repository
public class CodegenColumnRepository extends BaseRepository<CodegenColumnDO, ObjectId> {
    public CodegenColumnRepository(MongoTemplate mongoOperations) {
        super(CodegenColumnDO.class, mongoOperations);
    }

    public List<CodegenColumnDO> selectListByTableId(String tableId) {
        return selectList(new LambdaQueryWrapperMongo<>(CodegenColumnDO.class)
                .eq(CodegenColumnDO::getTableId, tableId)
                .orderByAsc(CodegenColumnDO::getId));
    }

    public CodegenColumnDO selectById(String id) {
        LambdaQueryWrapperMongo<CodegenColumnDO> queryWrapper = new LambdaQueryWrapperMongo<>(CodegenColumnDO.class)
                .eq(CodegenColumnDO::getId, id);
        return findOne(queryWrapper).orElse(null);
    }

    public void deleteListByTableId(String tableId) {
        LambdaQueryWrapperMongo<CodegenColumnDO> queryWrapper = new LambdaQueryWrapperMongo<>(CodegenColumnDO.class)
                .eq(CodegenColumnDO::getTableId, tableId);
        deleteByQuery(queryWrapper);
    }

    public void deleteBatchIds(Set<String> ids) {
        LambdaQueryWrapperMongo<CodegenColumnDO> queryWrapper = new LambdaQueryWrapperMongo<>(CodegenColumnDO.class)
                .in(CodegenColumnDO::getId, ids);
        deleteByQuery(queryWrapper);
    }

    public long deleteById(String id) {
        LambdaQueryWrapperMongo<CodegenColumnDO> queryWrapper = new LambdaQueryWrapperMongo<>(CodegenColumnDO.class)
                .eq(CodegenColumnDO::getId, id);
        return deleteByQuery(queryWrapper).getDeletedCount();
    }
}
