package net.iofun.mdis.module.infra.service.config;

import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.module.infra.controller.admin.config.vo.ConfigPageReqVO;
import net.iofun.mdis.module.infra.controller.admin.config.vo.ConfigSaveReqVO;
import net.iofun.mdis.module.infra.convert.config.ConfigConvert;
import net.iofun.mdis.module.infra.dal.dataobject.config.ConfigDO;
import net.iofun.mdis.module.infra.dal.repository.config.ConfigRepository;
import net.iofun.mdis.module.infra.enums.config.ConfigTypeEnum;
import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static net.iofun.mdis.module.infra.enums.ErrorCodeConstants.*;

/**
 * 参数配置 Service 实现类
 */
@Service
@Slf4j
@Validated
public class ConfigServiceImpl implements ConfigService {

    @Resource
    private ConfigRepository configRepository;

    @Override
    public String createConfig(ConfigSaveReqVO createReqVO) {
        // 校验参数配置 key 的唯一性
        validateConfigKeyUnique(null, createReqVO.getKey());

        // 插入参数配置
        ConfigDO config = ConfigConvert.INSTANCE.convert(createReqVO);
        config.setType(ConfigTypeEnum.CUSTOM.getType());
        configRepository.insert(config, getLoginUser());
        return config.getId().toString();
    }

    @Override
    public void updateConfig(ConfigSaveReqVO updateReqVO) {
        // 校验自己存在
        validateConfigExists(updateReqVO.getId());
        // 校验参数配置 key 的唯一性
        validateConfigKeyUnique(updateReqVO.getId(), updateReqVO.getKey());

        // 更新参数配置
        ConfigDO updateObj = ConfigConvert.INSTANCE.convert(updateReqVO);
        configRepository.updateById(updateObj, getLoginUser());
    }

    @Override
    public void deleteConfig(String id) {
        // 校验配置存在
        ConfigDO config = validateConfigExists(new ObjectId(id));
        // 内置配置，不允许删除
        if (ConfigTypeEnum.SYSTEM.getType().equals(config.getType())) {
            throw exception(CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE);
        }
        // 删除
        configRepository.deleteById(id, getLoginUser());
    }

    @Override
    public ConfigDO getConfig(String id) {
        return configRepository.selectById(id);
    }

    @Override
    public ConfigDO getConfigByKey(String key) {
        return configRepository.selectByKey(key);
    }

    @Override
    public PageResult<ConfigDO> getConfigPage(ConfigPageReqVO pageReqVO) {
        return configRepository.selectPage(pageReqVO);
    }

    @VisibleForTesting
    public ConfigDO validateConfigExists(ObjectId id) {
        if (id == null) {
            return null;
        }
        ConfigDO config = configRepository.selectById(id.toString());
        if (config == null) {
            throw exception(CONFIG_NOT_EXISTS);
        }
        return config;
    }

    @VisibleForTesting
    public void validateConfigKeyUnique(ObjectId id, String key) {
        ConfigDO config = configRepository.selectByKey(key);
        if (config == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的参数配置
        if (id == null) {
            throw exception(CONFIG_KEY_DUPLICATE);
        }
        if (!config.getId().equals(id)) {
            throw exception(CONFIG_KEY_DUPLICATE);
        }
    }

}
