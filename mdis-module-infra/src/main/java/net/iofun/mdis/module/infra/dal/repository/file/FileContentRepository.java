package net.iofun.mdis.module.infra.dal.repository.file;

import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.module.infra.dal.dataobject.file.FileContentDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class FileContentRepository extends BaseRepository<FileContentDO, ObjectId> {
    public FileContentRepository(MongoTemplate mongoOperations) {
        super(FileContentDO.class, mongoOperations);
    }

    public void deleteByConfigIdAndPath(String configId, String path) {
        deleteByQuery(new LambdaQueryWrapperMongo<>(FileContentDO.class)
                .eq(FileContentDO::getConfigId, configId)
                .eq(FileContentDO::getPath, path));
    }

    public List<FileContentDO> selectListByConfigIdAndPath(String configId, String path) {
        return selectList(new LambdaQueryWrapperMongo<>(FileContentDO.class)
                .eq(FileContentDO::getConfigId, configId)
                .eq(FileContentDO::getPath, path));
    }
}
