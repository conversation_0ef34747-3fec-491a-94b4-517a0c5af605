package net.iofun.mdis.module.infra.dal.repository.file;

import com.mongodb.client.result.UpdateResult;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.controller.admin.file.vo.config.FileConfigPageReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.file.FileConfigDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class FileConfigRepository extends BaseRepository<FileConfigDO, ObjectId> {
    public FileConfigRepository(MongoTemplate mongoOperations) {
        super(FileConfigDO.class, mongoOperations);
    }

    public PageResult<FileConfigDO> selectPage(FileConfigPageReqVO reqVO) {
        LambdaQueryWrapperMongo<FileConfigDO> queryWrapper = new LambdaQueryWrapperMongo<>(FileConfigDO.class)
                .likeIfPresent(FileConfigDO::getName, reqVO.getName())
                .eqIfPresent(FileConfigDO::getStorage, reqVO.getStorage())
                .betweenIfPresent(FileConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(FileConfigDO::getId);
        return selectPage(reqVO, queryWrapper);
    }

    public FileConfigDO selectByMaster() {
        LambdaQueryWrapperMongo<FileConfigDO> queryWrapper = new LambdaQueryWrapperMongo<>(FileConfigDO.class)
                .eq(FileConfigDO::getMaster, true);
        return findOne(queryWrapper).orElse(null);
    }

    public FileConfigDO selectById(String strId) {
        return selectById(new LambdaQueryWrapperMongo<>(FileConfigDO.class)
                .eq(FileConfigDO::getId, strId)).orElse(null);
    }

    public UpdateResult updateBatch(String fieldName, Boolean value, LoginUser loginUser) {
        return updateMany(new LambdaQueryWrapperMongo<>(FileConfigDO.class),
                new Update().set(fieldName, value), loginUser);
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        return softDeleteByQuery(new LambdaQueryWrapperMongo<>(FileConfigDO.class)
                .eq(FileConfigDO::getId, id), loginUser);
    }
}
