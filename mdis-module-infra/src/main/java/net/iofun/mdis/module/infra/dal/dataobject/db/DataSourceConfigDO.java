package net.iofun.mdis.module.infra.dal.dataobject.db;

import net.iofun.mdis.framework.common.entity.BaseEntity;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 数据源配置
 *
 * <AUTHOR>
 */
@Document("infra_data_source_config")
@Data
public class DataSourceConfigDO extends BaseEntity {

    /**
     * 主键编号 - Master 数据源
     */
    public static final String ID_MASTER = "000000000000000000000000";

    /**
     * 主键编号
     */
//    private Long id;
    /**
     * 连接名
     */
    private String name;

    /**
     * 数据源连接
     */
    private String url;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
//    @TableField(typeHandler = EncryptTypeHandler.class)
    private String password;

}
