package net.iofun.mdis.module.infra.dal.dataobject.demo.demo03;

import net.iofun.mdis.framework.common.entity.BaseEntity;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 学生课程 DO
 *
 * <AUTHOR>
 */
@Document("mdis_demo03_course")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Demo03CourseDO extends BaseEntity {

    /**
     * 编号
     */
//    @TableId
//    private Long id;
    /**
     * 学生编号
     */
    @Field("student_id")
    private String studentId;
    /**
     * 名字
     */
    private String name;
    /**
     * 分数
     */
    private Integer score;

}