package net.iofun.mdis.module.infra.dal.dataobject.job;

import net.iofun.mdis.framework.common.entity.BaseEntity;
import net.iofun.mdis.framework.quartz.core.handler.JobHandler;
import net.iofun.mdis.module.infra.enums.job.JobLogStatusEnum;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

/**
 * 定时任务的执行日志
 *
 * <AUTHOR>
 */
@Document("infra_job_log")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobLogDO extends BaseEntity {

    /**
     * 日志编号
     */
//    private Long id;
    /**
     * 任务编号
     *
     * 关联 {@link JobDO#getId()}
     */
    @Field("job_id")
    private String jobId;
    /**
     * 处理器的名字
     *
     * 冗余字段 {@link JobDO#getHandlerName()}
     */
    @Field("handler_name")
    private String handlerName;
    /**
     * 处理器的参数
     *
     * 冗余字段 {@link JobDO#getHandlerParam()}
     */
    @Field("handler_param")
    private String handlerParam;
    /**
     * 第几次执行
     *
     * 用于区分是不是重试执行。如果是重试执行，则 index 大于 1
     */
    @Field("execute_index")
    private Integer executeIndex;

    /**
     * 开始执行时间
     */
    @Field("begin_time")
    private LocalDateTime beginTime;
    /**
     * 结束执行时间
     */
    @Field("end_time")
    private LocalDateTime endTime;
    /**
     * 执行时长，单位：毫秒
     */
    private Integer duration;
    /**
     * 状态
     *
     * 枚举 {@link JobLogStatusEnum}
     */
    private Integer status;
    /**
     * 结果数据
     *
     * 成功时，使用 {@link JobHandler#execute(String)} 的结果
     * 失败时，使用 {@link JobHandler#execute(String)} 的异常堆栈
     */
    private String result;

}
