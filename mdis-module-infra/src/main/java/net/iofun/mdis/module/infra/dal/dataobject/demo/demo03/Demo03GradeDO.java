package net.iofun.mdis.module.infra.dal.dataobject.demo.demo03;

import net.iofun.mdis.framework.common.entity.BaseEntity;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 学生班级 DO
 *
 * <AUTHOR>
 */
@Document("mdis_demo03_grade")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Demo03GradeDO extends BaseEntity {

    /**
     * 编号
     */
//    @TableId
//    private Long id;
    /**
     * 学生编号
     */
    @Field("student_id")
    private String studentId;
    /**
     * 名字
     */
    private String name;
    /**
     * 班主任
     */
    private String teacher;

}