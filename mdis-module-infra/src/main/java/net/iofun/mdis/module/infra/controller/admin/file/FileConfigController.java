package net.iofun.mdis.module.infra.controller.admin.file;

import net.iofun.mdis.framework.common.pojo.CommonResult;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.module.infra.convert.file.FileConfigConvert;
import net.iofun.mdis.module.infra.controller.admin.file.vo.config.FileConfigPageReqVO;
import net.iofun.mdis.module.infra.controller.admin.file.vo.config.FileConfigRespVO;
import net.iofun.mdis.module.infra.controller.admin.file.vo.config.FileConfigSaveReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.file.FileConfigDO;
import net.iofun.mdis.module.infra.service.file.FileConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;

import static net.iofun.mdis.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 文件配置")
@RestController
@RequestMapping("/infra/file-config")
@Validated
public class FileConfigController {

    @Resource
    private FileConfigService fileConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建文件配置")
    @PreAuthorize("@ss.hasPermission('infra:file-config:create')")
    public CommonResult<String> createFileConfig(@Valid @RequestBody FileConfigSaveReqVO createReqVO) {
        return success(fileConfigService.createFileConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新文件配置")
    @PreAuthorize("@ss.hasPermission('infra:file-config:update')")
    public CommonResult<Boolean> updateFileConfig(@Valid @RequestBody FileConfigSaveReqVO updateReqVO) {
        fileConfigService.updateFileConfig(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-master")
    @Operation(summary = "更新文件配置为 Master")
    @PreAuthorize("@ss.hasPermission('infra:file-config:update')")
    public CommonResult<Boolean> updateFileConfigMaster(@RequestParam("id") String id) {
        fileConfigService.updateFileConfigMaster(id);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除文件配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('infra:file-config:delete')")
    public CommonResult<Boolean> deleteFileConfig(@RequestParam("id") String id) {
        fileConfigService.deleteFileConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得文件配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('infra:file-config:query')")
    public CommonResult<FileConfigRespVO> getFileConfig(@RequestParam("id") String id) {
        FileConfigDO config = fileConfigService.getFileConfig(id);
        if (config == null) {
            return success(null);
        }
        
        // 手动创建和设置对象，不使用BeanUtils
        FileConfigRespVO respVO = FileConfigConvert.INSTANCE.convert2FileConfigRespVO(config);
        return success(respVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得文件配置分页")
    @PreAuthorize("@ss.hasPermission('infra:file-config:query')")
    public CommonResult<PageResult<FileConfigRespVO>> getFileConfigPage(@Valid FileConfigPageReqVO pageVO) {
        PageResult<FileConfigDO> pageResult = fileConfigService.getFileConfigPage(pageVO);
        // 手动处理 FileConfigDO 到 FileConfigRespVO 的转换，避免BeanUtils自动转换config字段
        List<FileConfigRespVO> respVOList = new ArrayList<>(pageResult.getList().size());
        for (FileConfigDO configDO : pageResult.getList()) {
            // 手动创建和设置对象，不使用BeanUtils
            FileConfigRespVO respVO = FileConfigConvert.INSTANCE.convert2FileConfigRespVO(configDO);
            respVOList.add(respVO);
        }
        return success(new PageResult<>(respVOList, pageResult.getTotal()));
    }

    @GetMapping("/test")
    @Operation(summary = "测试文件配置是否正确")
    @PreAuthorize("@ss.hasPermission('infra:file-config:query')")
    public CommonResult<String> testFileConfig(@RequestParam("id") String id) throws Exception {
        String url = fileConfigService.testFileConfig(id);
        return success(url);
    }
}
