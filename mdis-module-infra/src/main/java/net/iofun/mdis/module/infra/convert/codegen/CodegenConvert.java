package net.iofun.mdis.module.infra.convert.codegen;

import net.iofun.mdis.framework.common.util.collection.CollectionUtils;
import net.iofun.mdis.framework.common.util.object.BeanUtils;
import net.iofun.mdis.module.infra.controller.admin.codegen.vo.CodegenDetailRespVO;
import net.iofun.mdis.module.infra.controller.admin.codegen.vo.CodegenPreviewRespVO;
import net.iofun.mdis.module.infra.controller.admin.codegen.vo.column.CodegenColumnRespVO;
import net.iofun.mdis.module.infra.controller.admin.codegen.vo.table.CodegenTableRespVO;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.CodegenColumnDO;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.CodegenTableDO;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.TableFieldMongo;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.TableInfoMongo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

@Mapper
public interface CodegenConvert {

    CodegenConvert INSTANCE = Mappers.getMapper(CodegenConvert.class);

    // ========== TableInfo 相关 ==========

    @Mappings({
            @Mapping(source = "name", target = "tableName"),
            @Mapping(source = "comment", target = "tableComment"),
    })
//    CodegenTableDO convert(TableInfo bean);
    CodegenTableDO convert(TableInfoMongo bean);

    List<CodegenColumnDO> convertList(List<TableFieldMongo> list);

    @Mappings({
            @Mapping(source = "name", target = "columnName"),
            @Mapping(source = "comment", target = "columnComment"),
    })
    CodegenColumnDO convert(TableFieldMongo bean);

    // ========== 其它 ==========

    default CodegenDetailRespVO convert(CodegenTableDO table, List<CodegenColumnDO> columns) {
        CodegenDetailRespVO respVO = new CodegenDetailRespVO();
        respVO.setTable(BeanUtils.toBean(table, CodegenTableRespVO.class));
        respVO.setColumns(BeanUtils.toBean(columns, CodegenColumnRespVO.class));
        return respVO;
    }

    default List<CodegenPreviewRespVO> convert(Map<String, String> codes) {
        return CollectionUtils.convertList(codes.entrySet(),
                entry -> new CodegenPreviewRespVO().setFilePath(entry.getKey()).setCode(entry.getValue()));
    }

}
