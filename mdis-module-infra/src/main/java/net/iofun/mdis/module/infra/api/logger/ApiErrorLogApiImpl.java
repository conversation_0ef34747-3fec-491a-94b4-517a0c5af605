package net.iofun.mdis.module.infra.api.logger;

import net.iofun.mdis.framework.common.biz.infra.logger.ApiErrorLogCommonApi;
import net.iofun.mdis.framework.common.biz.infra.logger.dto.ApiErrorLogCreateReqDTO;
import net.iofun.mdis.module.infra.service.logger.ApiErrorLogService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

/**
 * API 访问日志的 API 接口
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ApiErrorLogApiImpl implements ApiErrorLogCommonApi {

    @Resource
    private ApiErrorLogService apiErrorLogService;

    @Override
    public void createApiErrorLog(ApiErrorLogCreateReqDTO createDTO) {
        apiErrorLogService.createApiErrorLog(createDTO);
    }

}
