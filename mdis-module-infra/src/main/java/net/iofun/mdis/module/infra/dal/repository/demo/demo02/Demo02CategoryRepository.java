package net.iofun.mdis.module.infra.dal.repository.demo.demo02;

import com.mongodb.client.result.UpdateResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.controller.admin.demo.demo02.vo.Demo02CategoryListReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.demo.demo02.Demo02CategoryDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class Demo02CategoryRepository extends BaseRepository<Demo02CategoryDO, ObjectId> {
    public Demo02CategoryRepository(MongoTemplate mongoOperations) {
        super(Demo02CategoryDO.class, mongoOperations);
    }

    public List<Demo02CategoryDO> selectList(Demo02CategoryListReqVO reqVO) {
        LambdaQueryWrapperMongo<Demo02CategoryDO> queryWrapper = new LambdaQueryWrapperMongo<>(Demo02CategoryDO.class)
                .likeIfPresent(Demo02CategoryDO::getName, reqVO.getName())
                .eqIfPresent(Demo02CategoryDO::getParentId, reqVO.getParentId())
                .betweenIfPresent(Demo02CategoryDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(Demo02CategoryDO::getId);
        return selectList(queryWrapper);
    }

	public Demo02CategoryDO selectByParentIdAndName(String parentId, String name) {
        LambdaQueryWrapperMongo<Demo02CategoryDO> queryWrapper = new LambdaQueryWrapperMongo<>(Demo02CategoryDO.class)
                .eq(Demo02CategoryDO::getParentId, parentId)
                .eq(Demo02CategoryDO::getName, name);
        return findOne(queryWrapper).orElse(null);
	}

    public Long selectCountByParentId(String parentId) {
        LambdaQueryWrapperMongo<Demo02CategoryDO> queryWrapper = new LambdaQueryWrapperMongo<>(Demo02CategoryDO.class)
                .eq(Demo02CategoryDO::getParentId, parentId);
        return count(queryWrapper);
    }

    public Demo02CategoryDO selectById(String id) {
        LambdaQueryWrapperMongo<Demo02CategoryDO> queryWrapper = new LambdaQueryWrapperMongo<>(Demo02CategoryDO.class)
                .eqIfPresent(Demo02CategoryDO::getId, id);
        return findOne(queryWrapper).orElse(null);
    }

    public boolean validateExists(ObjectId id) {
        return existsByQuery(new LambdaQueryWrapperMongo<>(Demo02CategoryDO.class)
                .eq(Demo02CategoryDO::getId, id));
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        return softDeleteByQuery(new LambdaQueryWrapperMongo<>(Demo02CategoryDO.class)
                .eq(Demo02CategoryDO::getId, id), loginUser);
    }
}
