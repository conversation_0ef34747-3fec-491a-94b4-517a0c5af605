package net.iofun.mdis.module.infra.controller.admin.demo.demo02.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import net.iofun.mdis.framework.common.vo.BaseVO;

@Schema(description = "管理后台 - 示例分类新增/修改 Request VO")
@Data
public class Demo02CategorySaveReqVO extends BaseVO {

    @Schema(description = "名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "名字不能为空")
    private String name;

    @Schema(description = "父级编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "6080")
    @NotNull(message = "父级编号不能为空")
    private String parentId;

}