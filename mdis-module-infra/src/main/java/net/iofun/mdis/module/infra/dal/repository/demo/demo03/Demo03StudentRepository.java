package net.iofun.mdis.module.infra.dal.repository.demo.demo03;

import com.mongodb.client.result.UpdateResult;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.controller.admin.demo.demo03.vo.Demo03StudentPageReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.demo.demo03.Demo03StudentDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class Demo03StudentRepository extends BaseRepository<Demo03StudentDO, ObjectId> {
    public Demo03StudentRepository(MongoTemplate mongoOperations) {
        super(Demo03StudentDO.class, mongoOperations);
    }

    public PageResult<Demo03StudentDO> selectPage(Demo03StudentPageReqVO reqVO) {
        LambdaQueryWrapperMongo<Demo03StudentDO> queryWrapper = new LambdaQueryWrapperMongo<>(Demo03StudentDO.class)
                .likeIfPresent(Demo03StudentDO::getName, reqVO.getName())
                .eqIfPresent(Demo03StudentDO::getSex, reqVO.getSex())
                .eqIfPresent(Demo03StudentDO::getDescription, reqVO.getDescription())
                .betweenIfPresent(Demo03StudentDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(Demo03StudentDO::getId);
        return selectPage(reqVO, queryWrapper);
    }

    public Demo03StudentDO selectById(String strId) {
        return selectById(new LambdaQueryWrapperMongo<>(Demo03StudentDO.class)
                .eq(Demo03StudentDO::getId, strId)).orElse(null);
    }

    public boolean validateExists(ObjectId id) {
        return existsByQuery(new LambdaQueryWrapperMongo<>(Demo03StudentDO.class)
                .eq(Demo03StudentDO::getId, id));
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        return softDeleteByQuery(new LambdaQueryWrapperMongo<>(Demo03StudentDO.class)
                .eq(Demo03StudentDO::getId, id), loginUser);
    }
}
