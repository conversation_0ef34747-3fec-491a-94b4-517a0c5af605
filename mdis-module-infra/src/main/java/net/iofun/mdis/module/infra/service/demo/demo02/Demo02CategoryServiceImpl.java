package net.iofun.mdis.module.infra.service.demo.demo02;

import net.iofun.mdis.framework.common.util.object.BeanUtils;
import net.iofun.mdis.module.infra.controller.admin.demo.demo02.vo.Demo02CategoryListReqVO;
import net.iofun.mdis.module.infra.controller.admin.demo.demo02.vo.Demo02CategorySaveReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.demo.demo02.Demo02CategoryDO;
import net.iofun.mdis.module.infra.dal.repository.demo.demo02.Demo02CategoryRepository;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static net.iofun.mdis.module.infra.enums.ErrorCodeConstants.*;

/**
 * 示例分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class Demo02CategoryServiceImpl implements Demo02CategoryService {

    @Resource
    private Demo02CategoryRepository demo02CategoryRepository;

    @Override
    public String createDemo02Category(Demo02CategorySaveReqVO createReqVO) {
        // 校验父级编号的有效性
        validateParentDemo02Category(null, createReqVO.getParentId());
        // 校验名字的唯一性
        validateDemo02CategoryNameUnique(null, createReqVO.getParentId(), createReqVO.getName());

        // 插入
        Demo02CategoryDO demo02Category = BeanUtils.toBean(createReqVO, Demo02CategoryDO.class);
        demo02CategoryRepository.insert(demo02Category, getLoginUser());
        // 返回
        return demo02Category.getId().toString();
    }

    @Override
    public void updateDemo02Category(Demo02CategorySaveReqVO updateReqVO) {
        // 校验存在
        validateDemo02CategoryExists(updateReqVO.getId());
        // 校验父级编号的有效性
        validateParentDemo02Category(updateReqVO.getId().toHexString(), updateReqVO.getParentId());
        // 校验名字的唯一性
        validateDemo02CategoryNameUnique(updateReqVO.getId().toHexString(), updateReqVO.getParentId(), updateReqVO.getName());

        // 更新
        Demo02CategoryDO updateObj = BeanUtils.toBean(updateReqVO, Demo02CategoryDO.class);
        demo02CategoryRepository.updateById(updateObj, getLoginUser());
    }

    @Override
    public void deleteDemo02Category(String id) {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        validateDemo02CategoryExists(oid);
        // 校验是否有子示例分类
        if (demo02CategoryRepository.selectCountByParentId(id) > 0) {
            throw exception(DEMO02_CATEGORY_EXITS_CHILDREN);
        }
        // 删除
        demo02CategoryRepository.deleteById(oid, getLoginUser());
    }

    private void validateDemo02CategoryExists(ObjectId id) {
        if (!demo02CategoryRepository.validateExists(id)) {
            throw exception(DEMO02_CATEGORY_NOT_EXISTS);
        }
    }

    private void validateParentDemo02Category(String id, String parentId) {
        if (parentId == null || Demo02CategoryDO.PARENT_ID_ROOT.equals(parentId)) {
            return;
        }
        // 1. 不能设置自己为父示例分类
        if (Objects.equals(id, parentId)) {
            throw exception(DEMO02_CATEGORY_PARENT_ERROR);
        }
        // 2. 父示例分类不存在
        Demo02CategoryDO parentDemo02Category = demo02CategoryRepository.selectById(parentId);
        if (parentDemo02Category == null) {
            throw exception(DEMO02_CATEGORY_PARENT_NOT_EXITS);
        }
        // 3. 递归校验父示例分类，如果父示例分类是自己的子示例分类，则报错，避免形成环路
        if (id == null) { // id 为空，说明新增，不需要考虑环路
            return;
        }
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            // 3.1 校验环路
            parentId = parentDemo02Category.getParentId();
            if (Objects.equals(id, parentId)) {
                throw exception(DEMO02_CATEGORY_PARENT_IS_CHILD);
            }
            // 3.2 继续递归下一级父示例分类
            if (parentId == null || Demo02CategoryDO.PARENT_ID_ROOT.equals(parentId)) {
                break;
            }
            parentDemo02Category = demo02CategoryRepository.selectById(parentId);
            if (parentDemo02Category == null) {
                break;
            }
        }
    }

    private void validateDemo02CategoryNameUnique(String id, String parentId, String name) {
        Demo02CategoryDO demo02Category = demo02CategoryRepository.selectByParentIdAndName(parentId, name);
        if (demo02Category == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的示例分类
        if (id == null) {
            throw exception(DEMO02_CATEGORY_NAME_DUPLICATE);
        }
        if (!Objects.equals(demo02Category.getId(), id)) {
            throw exception(DEMO02_CATEGORY_NAME_DUPLICATE);
        }
    }

    @Override
    public Demo02CategoryDO getDemo02Category(String id) {
        return demo02CategoryRepository.selectById(id);
    }

    @Override
    public List<Demo02CategoryDO> getDemo02CategoryList(Demo02CategoryListReqVO listReqVO) {
        return demo02CategoryRepository.selectList(listReqVO);
    }

}