package net.iofun.mdis.module.infra.service.db;

import cn.hutool.core.lang.Assert;
import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.*;
import lombok.extern.slf4j.Slf4j;
import net.iofun.mdis.framework.common.util.object.BeanUtils;
import net.iofun.mdis.framework.mongo.core.util.MongoUtils;
import net.iofun.mdis.module.infra.controller.admin.db.vo.DataSourceConfigSaveReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.TableFieldMongo;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.TableInfoMongo;
import net.iofun.mdis.module.infra.dal.dataobject.db.DataSourceConfigDO;
import net.iofun.mdis.module.infra.dal.repository.db.DataSourceConfigRepository;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static net.iofun.mdis.module.infra.enums.ErrorCodeConstants.*;

/**
 * 数据源配置 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class DataSourceConfigServiceImpl implements DataSourceConfigService {

    @Resource
    private DataSourceConfigRepository dataSourceConfigRepository;

    //    @Resource
    //    private DynamicDataSourceProperties dynamicDataSourceProperties;

    @Value("${spring.data.mongodb.default.uri}")
    private String mongoUri;

    @Override
    public String createDataSourceConfig(DataSourceConfigSaveReqVO createReqVO) {
        DataSourceConfigDO config = BeanUtils.toBean(createReqVO, DataSourceConfigDO.class);
        validateConnectionOK(config);

        // 插入
        dataSourceConfigRepository.insert(config, getLoginUser());
        // 返回
        return config.getId().toString();
    }

    @Override
    public void updateDataSourceConfig(DataSourceConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateDataSourceConfigExists(updateReqVO.getId());
        DataSourceConfigDO updateObj = BeanUtils.toBean(updateReqVO, DataSourceConfigDO.class);
        validateConnectionOK(updateObj);

        // 更新
        dataSourceConfigRepository.updateById(updateObj, getLoginUser());
    }

    @Override
    public void deleteDataSourceConfig(String id) {
        // 校验存在
        validateDataSourceConfigExists(new ObjectId(id));
        // 删除
        dataSourceConfigRepository.deleteById(id, getLoginUser());
    }

    private void validateDataSourceConfigExists(ObjectId id) {
        if (dataSourceConfigRepository.selectById(id.toHexString()) == null) {
            throw exception(DATA_SOURCE_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public DataSourceConfigDO getDataSourceConfig(String id) {
        // 如果 id 为 "0"，默认为 master 的数据源
        if (Objects.equals(id, DataSourceConfigDO.ID_MASTER)) {
            return buildMasterDataSourceConfig();
        }
        // 从 DB 中读取
        return dataSourceConfigRepository.selectById(id);
    }

    @Override
    public List<DataSourceConfigDO> getDataSourceConfigList() {
        List<DataSourceConfigDO> result = dataSourceConfigRepository.selectList();
        // 补充 master 数据源, master数据源不在数据库表中, 而是通过配置生成的
        result.add(0, buildMasterDataSourceConfig());
        return result;
    }

    private void validateConnectionOK(DataSourceConfigDO config) {
        List<String> mongoDBTableList = getMongoDBTableList(config.getUrl());
        log.info("MongoDB 数据源配置连接测试成功, tables: {}", mongoDBTableList);

        //        boolean success = JdbcUtils.isConnectionOK(config.getUrl(), config.getUsername(), config.getPassword());
        //        if (!success) {
        //            throw exception(DATA_SOURCE_CONFIG_NOT_OK);
        //        }
    }

    private DataSourceConfigDO buildMasterDataSourceConfig() {
        //        String primary = dynamicDataSourceProperties.getPrimary();
        //        DataSourceProperty dataSourceProperty = dynamicDataSourceProperties.getDatasource().get(primary);
        DataSourceConfigDO configDO = new DataSourceConfigDO();
        configDO.setId(new ObjectId(DataSourceConfigDO.ID_MASTER));
        configDO.setName("master");
        configDO.setUrl(mongoUri);
        configDO.setUsername(null);
        configDO.setPassword(null);
        return configDO;
    }

    /**
     * Fetch MongoDB database collections as a list of table names.
     *
     * @param mongoUri the MongoDB connection URI.
     * @return a list of collection names in the MongoDB database.
     */
    @Override
    public List<String> getMongoDBTableList(String mongoUri) {
        Assert.notNull(mongoUri, "MongoDB connection URI cannot be empty!");
        if (!(mongoUri.startsWith("mongodb://") || mongoUri.startsWith("mongodb+srv://"))) {
            throw exception(DATA_SOURCE_CONFIG_NOT_MONGO);
        }
        try (MongoClient mongoClient = MongoUtils.createMongoClient(mongoUri)) {
            String dbName = MongoUtils.extractDatabaseName(mongoUri);
            Assert.notNull(dbName, "MongoDB database name cannot be empty!");
            return mongoClient.getDatabase(dbName).listCollectionNames().into(new java.util.ArrayList<>());
        } catch (Exception ex) {
            throw exception(DATA_SOURCE_CONFIG_MONGO_GET_TABLES_FAIL, ex.getMessage());
        }
    }

    @Override
    public List<TableInfoMongo> getMongoDBTableInfoList(String mongoUri, List<String> tableNames) {
        Assert.notNull(mongoUri, "MongoDB connection URI cannot be empty!");
        if (!(mongoUri.startsWith("mongodb://") || mongoUri.startsWith("mongodb+srv://"))) {
            throw exception(DATA_SOURCE_CONFIG_NOT_MONGO);
        }
        List<TableInfoMongo> tableInfos = new ArrayList<>();
        try (MongoClient mongoClient = MongoUtils.createMongoClient(mongoUri)) {
            String dbName = MongoUtils.extractDatabaseName(mongoUri);
            Assert.notNull(dbName, "MongoDB database name cannot be empty!");
            for (String tableName : tableNames) {
                TableInfoMongo tableInfo = getTableInfo(mongoClient, dbName, tableName);
                if (tableInfo == null) {
                    log.info("Database: {}, Table: {} not record found! ", dbName, tableName);
                    continue;
                }
                tableInfos.add(tableInfo);
            }
        } catch (Exception ex) {
            throw exception(DATA_SOURCE_CONFIG_MONGO_GET_TABLES_INFO_FAIL, ex.getMessage());
        }
        return tableInfos;
    }

    @Override
    public TableInfoMongo getTableInfo(MongoClient mongoClient, String dbName, String tableName) {
        MongoCollection<Document> collection = mongoClient.getDatabase(dbName).getCollection(tableName);
        Document document = collection.find().first();

        if (document == null) {
            return null;
        }

        List<TableFieldMongo> fields = document.keySet().stream()
                .map(key -> createTableField(document, key))
                .collect(Collectors.toList());

        return new TableInfoMongo()
                .setName(tableName)
                .setFields(fields);
    }

    @Override
    public TableInfoMongo getTableInfo(MongoTemplate mongoTemplate, String tableName) {
        Document document = mongoTemplate.getCollection(tableName).find().first();
        if (document == null) {
            return null;
        }

        List<TableFieldMongo> fields = document.keySet().stream()
                .map(key -> createTableField(document, key))
                .collect(Collectors.toList());

        return new TableInfoMongo()
                .setName(tableName)
                .setFields(fields);
    }

    private TableFieldMongo createTableField(Document document, String key) {
        Object value = document.get(key);
        String bsonType = MongoUtils.getBsonTypeName(value);

        return new TableFieldMongo()
                .setName(key)
                .setJavaType(MongoUtils.getJavaTypeName(value))
                .setDataType(bsonType);

    }
}
