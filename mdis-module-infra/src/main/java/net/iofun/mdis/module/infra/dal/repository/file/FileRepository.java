package net.iofun.mdis.module.infra.dal.repository.file;

import com.mongodb.client.result.UpdateResult;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.file.FileDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class FileRepository extends BaseRepository<FileDO, ObjectId> {
    public FileRepository(MongoTemplate mongoOperations) {
        super(FileDO.class, mongoOperations);
    }

    public PageResult<FileDO> selectPage(FilePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperMongo<>(FileDO.class)
                .likeIfPresent(FileDO::getPath, reqVO.getPath())
                .likeIfPresent(FileDO::getType, reqVO.getType())
                .betweenIfPresent(FileDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(FileDO::getId));
    }

    public FileDO selectById(String strId) {
        return selectById(new LambdaQueryWrapperMongo<>(FileDO.class)
                .eq(FileDO::getId, strId)).orElse(null);
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        return softDeleteByQuery(new LambdaQueryWrapperMongo<>(FileDO.class)
                .eq(FileDO::getId, id), loginUser);
    }
}
