package net.iofun.mdis.module.infra.framework.file.core.client.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import net.iofun.mdis.module.infra.dal.dataobject.file.FileContentDO;
import net.iofun.mdis.module.infra.dal.repository.file.FileContentRepository;
import net.iofun.mdis.module.infra.framework.file.core.client.AbstractFileClient;

import java.util.Comparator;
import java.util.List;

import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;

/**
 * 基于 DB 存储的文件客户端的配置类
 *
 * <AUTHOR>
 */
public class DBFileClient extends AbstractFileClient<DBFileClientConfig> {

    private FileContentRepository fileContentRepository;

    public DBFileClient(String id, DBFileClientConfig config) {
        super(id, config);
    }

    @Override
    protected void doInit() {
        fileContentRepository = SpringUtil.getBean(FileContentRepository.class);
    }

    @Override
    public String upload(byte[] content, String path, String type) {
        FileContentDO contentDO = new FileContentDO().setConfigId(getId())
                .setPath(path).setContent(content);
        fileContentRepository.insert(contentDO, getLoginUser());
        // 拼接返回路径
        return super.formatFileUrl(config.getDomain(), path);
    }

    @Override
    public void delete(String path) {
        fileContentRepository.deleteByConfigIdAndPath(getId(), path);
    }

    @Override
    public byte[] getContent(String path) {
        List<FileContentDO> list = fileContentRepository.selectListByConfigIdAndPath(getId(), path);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        // 排序后，拿 id 最大的，即最后上传的
        list.sort(Comparator.comparing(FileContentDO::getId));
        return CollUtil.getLast(list).getContent();
    }

}
