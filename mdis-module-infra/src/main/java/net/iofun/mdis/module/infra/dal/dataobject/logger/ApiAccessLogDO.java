package net.iofun.mdis.module.infra.dal.dataobject.logger;

import net.iofun.mdis.framework.apilog.core.enums.OperateTypeEnum;
import net.iofun.mdis.framework.common.enums.UserTypeEnum;
import net.iofun.mdis.framework.common.pojo.CommonResult;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

/**
 * API 访问日志
 *
 * <AUTHOR>
 */
@Document("infra_api_access_log")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiAccessLogDO extends BaseEntity {

    /**
     * {@link #requestParams} 的最大长度
     */
    public static final Integer REQUEST_PARAMS_MAX_LENGTH = 8000;

    /**
     * {@link #resultMsg} 的最大长度
     */
    public static final Integer RESULT_MSG_MAX_LENGTH = 512;

    /**
     * 编号
     */
//    @TableId
//    private Long id;
    /**
     * 链路追踪编号
     *
     * 一般来说，通过链路追踪编号，可以将访问日志，错误日志，链路追踪日志，logger 打印日志等，结合在一起，从而进行排错。
     */
    @Field("trace_id")
    private String traceId;
    /**
     * 用户编号
     */
    @Field("user_id")
    private String userId;
    /**
     * 用户类型
     *
     * 枚举 {@link UserTypeEnum}
     */
    @Field("user_type")
    private Integer userType;
    /**
     * 应用名
     *
     * 目前读取 `META-INF.application.name` 配置项
     */
    @Field("application_name")
    private String applicationName;

    // ========== 请求相关字段 ==========

    /**
     * 请求方法名
     */
    @Field("request_method")
    private String requestMethod;
    /**
     * 访问地址
     */
    @Field("request_url")
    private String requestUrl;
    /**
     * 请求参数
     *
     * query: Query String
     * body: Quest Body
     */
    @Field("request_params")
    private String requestParams;
    /**
     * 响应结果
     */
    @Field("response_body")
    private String responseBody;
    /**
     * 用户 IP
     */
    @Field("user_ip")
    private String userIp;
    /**
     * 浏览器 UA
     */
    @Field("user_agent")
    private String userAgent;

    // ========== 执行相关字段 ==========

    /**
     * 操作模块
     */
    @Field("operate_module")
    private String operateModule;
    /**
     * 操作名
     */
    @Field("operate_name")
    private String operateName;
    /**
     * 操作分类
     *
     * 枚举 {@link OperateTypeEnum}
     */
    @Field("operate_type")
    private Integer operateType;

    /**
     * 开始请求时间
     */
    @Field("begin_time")
    private LocalDateTime beginTime;
    /**
     * 结束请求时间
     */
    @Field("end_time")
    private LocalDateTime endTime;
    /**
     * 执行时长，单位：毫秒
     */
    private Integer duration;

    /**
     * 结果码
     *
     * 目前使用的 {@link CommonResult#getCode()} 属性
     */
    @Field("result_code")
    private Integer resultCode;
    /**
     * 结果提示
     *
     * 目前使用的 {@link CommonResult#getMsg()} 属性
     */
    @Field("result_msg")
    private String resultMsg;

}
