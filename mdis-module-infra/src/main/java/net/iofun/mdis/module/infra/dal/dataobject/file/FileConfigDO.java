package net.iofun.mdis.module.infra.dal.dataobject.file;

import com.alibaba.fastjson.JSONObject;
import net.iofun.mdis.framework.common.util.json.JsonUtils;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import net.iofun.mdis.framework.tenant.core.aop.TenantIgnore;
import net.iofun.mdis.module.infra.framework.file.core.client.FileClientConfig;
import net.iofun.mdis.module.infra.framework.file.core.client.db.DBFileClientConfig;
import net.iofun.mdis.module.infra.framework.file.core.client.ftp.FtpFileClientConfig;
import net.iofun.mdis.module.infra.framework.file.core.client.local.LocalFileClientConfig;
import net.iofun.mdis.module.infra.framework.file.core.client.s3.S3FileClientConfig;
import net.iofun.mdis.module.infra.framework.file.core.enums.FileStorageEnum;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 文件配置表
 *
 * <AUTHOR>
 */
@Document("infra_file_config")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TenantIgnore
public class FileConfigDO extends BaseEntity {

    /**
     * 配置编号，数据库自增
     */
//    private Long id;
    /**
     * 配置名
     */
    private String name;
    /**
     * 存储器
     *
     * 枚举 {@link FileStorageEnum}
     */
    private Integer storage;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否为主配置
     *
     * 由于我们可以配置多个文件配置，默认情况下，使用主配置进行文件的上传
     */
    private Boolean master;

    /**
     * 支付渠道配置，JSON 字符串
     */
    private JSONObject config;

    /**
     * 获取配置，反序列化为对应的配置类
     */
    public FileClientConfig getClientConfig() {
        if (config == null) {
            return null;
        }
        // 根据 storage 字段判断具体的配置类型
        FileStorageEnum storageEnum = FileStorageEnum.getByStorage(storage);
        Class<? extends FileClientConfig> configClass;
        switch (storageEnum) {
            case LOCAL -> configClass = LocalFileClientConfig.class;
            case FTP -> configClass = FtpFileClientConfig.class;
            case DB -> configClass = DBFileClientConfig.class;
            case S3 -> configClass = S3FileClientConfig.class;
            default -> throw new IllegalArgumentException("不支持的存储类型：" + storage);
        }
        return config.toJavaObject(configClass);
    }

    /**
     * 设置配置，将配置类序列化为 JSONObject
     */
    public void setClientConfig(FileClientConfig clientConfig) {
        if (clientConfig == null) {
            this.config = null;
            return;
        }
        this.config = JSONObject.parseObject(JsonUtils.toJsonString(clientConfig));
    }

}
