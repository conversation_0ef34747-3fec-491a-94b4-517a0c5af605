package net.iofun.mdis.module.infra.convert.file;

import cn.hutool.core.lang.ObjectId;
import net.iofun.mdis.module.infra.controller.admin.file.vo.config.FileConfigRespVO;
import net.iofun.mdis.module.infra.controller.admin.file.vo.config.FileConfigSaveReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.file.FileConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 文件配置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface FileConfigConvert {

    FileConfigConvert INSTANCE = Mappers.getMapper(FileConfigConvert.class);

    // -------- others --------
    default FileConfigDO convertFileConfigSaveReqVO2FileConfigDO(FileConfigSaveReqVO bean) {
        FileConfigDO fileConfigDO = new FileConfigDO();
        if (null != bean.getId()) {
            fileConfigDO.setId(bean.getId());
        }
        fileConfigDO.setId(bean.getId());
        fileConfigDO.setName(bean.getName());
        fileConfigDO.setStorage(bean.getStorage());
        fileConfigDO.setRemark(bean.getRemark());
        return fileConfigDO;

    }
    default FileConfigRespVO convert2FileConfigRespVO(FileConfigDO configDO) {
        FileConfigRespVO respVO = new FileConfigRespVO();
            respVO.setId(configDO.getId());
            respVO.setName(configDO.getName());
            respVO.setStorage(configDO.getStorage());
            respVO.setMaster(configDO.getMaster());
            respVO.setRemark(configDO.getRemark());
            respVO.setCreateTime(configDO.getCreateTime());
            // 临时解决方案：不解析config字段，避免类型不匹配错误
            respVO.setConfig(configDO.getClientConfig());
        return respVO;
    }

}
