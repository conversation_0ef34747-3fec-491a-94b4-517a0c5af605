package net.iofun.mdis.module.infra.dal.repository.job;

import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.controller.admin.job.vo.log.JobLogPageReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.job.JobLogDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Repository
public class JobLogRepository extends BaseRepository<JobLogDO, ObjectId> {
    public JobLogRepository(MongoTemplate mongoOperations) {
        super(JobLogDO.class, mongoOperations);
    }

    public PageResult<JobLogDO> selectPage(JobLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperMongo<>(JobLogDO.class)
                .eqIfPresent(JobLogDO::getJobId, reqVO.getJobId())
                .likeIfPresent(JobLogDO::getHandlerName, reqVO.getHandlerName())
                .gteIfPresent(JobLogDO::getBeginTime, reqVO.getBeginTime())
                .lteIfPresent(JobLogDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(JobLogDO::getStatus, reqVO.getStatus())
                .orderByDesc(JobLogDO::getId));
    }

    /**
     * 物理删除指定时间之前的日志
     *
     * @param createTime 最大时间
     * @param limit 删除条数，防止一次删除太多
     * @return 删除条数
     */
    public Integer deleteByCreateTimeLt(LocalDateTime createTime, Integer limit) {
        LambdaQueryWrapperMongo<JobLogDO> queryWrapper = new LambdaQueryWrapperMongo<>(JobLogDO.class)
                .lteIfPresent(JobLogDO::getCreateTime, createTime)
                .limitIfPresent(limit);
        return  (int) deleteByQuery(queryWrapper).getDeletedCount();
    }

    public JobLogDO selectById(String strId) {
        return selectById(new LambdaQueryWrapperMongo<>(JobLogDO.class)
                .eq(JobLogDO::getId, strId)).orElse(null);
    }
}
