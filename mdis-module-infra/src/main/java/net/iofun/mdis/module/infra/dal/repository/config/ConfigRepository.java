package net.iofun.mdis.module.infra.dal.repository.config;

import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.controller.admin.config.vo.ConfigPageReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.config.ConfigDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class ConfigRepository extends BaseRepository<ConfigDO, ObjectId> {
    public ConfigRepository(MongoTemplate mongoOperations) {
        super(ConfigDO.class, mongoOperations);
    }

    public ConfigDO selectByKey(String key) {
        LambdaQueryWrapperMongo<ConfigDO> queryWrapper = new LambdaQueryWrapperMongo<>(ConfigDO.class)
                .eq(ConfigDO::getConfigKey, key);

        return findOne(queryWrapper).orElse(null);
    }

    public PageResult<ConfigDO> selectPage(ConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperMongo<>(ConfigDO.class)
                .likeIfPresent(ConfigDO::getName, reqVO.getName())
                .likeIfPresent(ConfigDO::getConfigKey, reqVO.getKey())
                .eqIfPresent(ConfigDO::getType, reqVO.getType())
                .betweenIfPresent(ConfigDO::getCreateTime, reqVO.getCreateTime()));
    }

    public ConfigDO selectById(String id) {
        LambdaQueryWrapperMongo<ConfigDO> queryWrapper = new LambdaQueryWrapperMongo<>(ConfigDO.class)
                .eq(ConfigDO::getId, id);
        return findOne(queryWrapper).orElse(null);
    }

    public void deleteById(String id, LoginUser loginUser) {
        LambdaQueryWrapperMongo<ConfigDO> queryWrapper = new LambdaQueryWrapperMongo<>(ConfigDO.class)
                .eq(ConfigDO::getId, id);
        softDeleteByQuery(queryWrapper, loginUser);
    }
}
