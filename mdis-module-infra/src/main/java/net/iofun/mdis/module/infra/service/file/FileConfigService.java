package net.iofun.mdis.module.infra.service.file;

import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.module.infra.framework.file.core.client.FileClient;
import net.iofun.mdis.module.infra.controller.admin.file.vo.config.FileConfigPageReqVO;
import net.iofun.mdis.module.infra.controller.admin.file.vo.config.FileConfigSaveReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.file.FileConfigDO;

import jakarta.validation.Valid;

/**
 * 文件配置 Service 接口
 *
 * <AUTHOR>
 */
public interface FileConfigService {

    /**
     * 创建文件配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createFileConfig(@Valid FileConfigSaveReqVO createReqVO);

    /**
     * 更新文件配置
     *
     * @param updateReqVO 更新信息
     */
    void updateFileConfig(@Valid FileConfigSaveReqVO updateReqVO);

    /**
     * 更新文件配置为 Master
     *
     * @param id 编号
     */
    void updateFileConfigMaster(String id);

    /**
     * 删除文件配置
     *
     * @param id 编号
     */
    void deleteFileConfig(String id);

    /**
     * 获得文件配置
     *
     * @param id 编号
     * @return 文件配置
     */
    FileConfigDO getFileConfig(String id);

    /**
     * 获得文件配置分页
     *
     * @param pageReqVO 分页查询
     * @return 文件配置分页
     */
    PageResult<FileConfigDO> getFileConfigPage(FileConfigPageReqVO pageReqVO);

    /**
     * 测试文件配置是否正确，通过上传文件
     *
     * @param id 编号
     * @return 文件 URL
     */
    String testFileConfig(String id) throws Exception;

    /**
     * 获得指定编号的文件客户端
     *
     * @param id 配置编号
     * @return 文件客户端
     */
    FileClient getFileClient(String id);

    /**
     * 获得 Master 文件客户端
     *
     * @return 文件客户端
     */
    FileClient getMasterFileClient();

}
