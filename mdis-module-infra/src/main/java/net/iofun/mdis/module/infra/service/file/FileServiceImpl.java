package net.iofun.mdis.module.infra.service.file;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.util.io.FileUtils;
import net.iofun.mdis.framework.common.util.object.BeanUtils;
import net.iofun.mdis.module.infra.controller.admin.file.vo.file.FileRespVO;
import net.iofun.mdis.module.infra.dal.repository.file.FileRepository;
import net.iofun.mdis.module.infra.framework.file.core.client.FileClient;
import net.iofun.mdis.module.infra.framework.file.core.client.s3.FilePresignedUrlRespDTO;
import net.iofun.mdis.module.infra.framework.file.core.utils.FileTypeUtils;
import net.iofun.mdis.module.infra.controller.admin.file.vo.file.FileCreateReqVO;
import net.iofun.mdis.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import net.iofun.mdis.module.infra.controller.admin.file.vo.file.FilePresignedUrlRespVO;
import net.iofun.mdis.module.infra.dal.dataobject.file.FileDO;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static net.iofun.mdis.module.infra.enums.ErrorCodeConstants.FILE_NOT_EXISTS;

/**
 * 文件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class FileServiceImpl implements FileService {

    @Resource
    private FileConfigService fileConfigService;

    @Resource
    private FileRepository fileRepository;

    @Override
    public PageResult<FileDO> getFilePage(FilePageReqVO pageReqVO) {
        return fileRepository.selectPage(pageReqVO);
    }

    @Override
    @SneakyThrows
    public String createFile(String name, String path, byte[] content) {
        // 计算默认的 path 名
        String type = FileTypeUtils.getMineType(content, name);
        if (StrUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name);
        }
        // 如果 name 为空，则使用 path 填充
        if (StrUtil.isEmpty(name)) {
            name = path;
        }

        // 上传到文件存储器
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "客户端(master) 不能为空");
        String url = client.upload(content, path, type);

        // 保存到数据库
        FileDO file = new FileDO();
        file.setConfigId(client.getId());
        file.setName(name);
        file.setPath(path);
        file.setUrl(url);
        file.setType(type);
        file.setSize(content.length);
        fileRepository.insert(file, getLoginUser());
        return url;
    }

    @Override
    public String createFile(FileCreateReqVO createReqVO) {
        FileDO file = BeanUtils.toBean(createReqVO, FileDO.class);
        fileRepository.insert(file, getLoginUser());
        return file.getId().toHexString();
    }

    @Override
    public void deleteFile(String id) throws Exception {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        FileDO file = validateFileExists(oid);

        // 从文件存储器中删除
        FileClient client = fileConfigService.getFileClient(file.getConfigId());
        Assert.notNull(client, "客户端({}) 不能为空", file.getConfigId());
        client.delete(file.getPath());

        // 删除记录
        fileRepository.deleteById(oid, getLoginUser());
    }

    private FileDO validateFileExists(ObjectId id) {
        FileDO fileDO = fileRepository.selectById(id.toHexString());
        if (fileDO == null) {
            throw exception(FILE_NOT_EXISTS);
        }
        return fileDO;
    }

    @Override
    public byte[] getFileContent(String configId, String path) throws Exception {
        FileClient client = fileConfigService.getFileClient(configId);
        Assert.notNull(client, "客户端({}) 不能为空", configId);
        return client.getContent(path);
    }

    @Override
    public FilePresignedUrlRespVO getFilePresignedUrl(String path) throws Exception {
        FileClient fileClient = fileConfigService.getMasterFileClient();
        FilePresignedUrlRespDTO presignedObjectUrl = fileClient.getPresignedObjectUrl(path);
        return BeanUtils.toBean(presignedObjectUrl, FilePresignedUrlRespVO.class,
                object -> object.setConfigId(fileClient.getId()));
    }

}
