package net.iofun.mdis.module.infra.dal.dataobject.file;

import net.iofun.mdis.framework.common.entity.BaseEntity;
import net.iofun.mdis.module.infra.framework.file.core.client.db.DBFileClient;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 文件内容表
 *
 * 专门用于存储 {@link DBFileClient} 的文件内容
 *
 * <AUTHOR>
 */
@Document("infra_file_content")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileContentDO extends BaseEntity {

    /**
     * 编号，数据库自增
     */
//    @TableId
//    private Long id;
    /**
     * 配置编号
     *
     * 关联 {@link FileConfigDO#getId()}
     */
    @Field("config_id")
    private String configId;
    /**
     * 路径，即文件名
     */
    private String path;
    /**
     * 文件内容
     */
    private byte[] content;

}
