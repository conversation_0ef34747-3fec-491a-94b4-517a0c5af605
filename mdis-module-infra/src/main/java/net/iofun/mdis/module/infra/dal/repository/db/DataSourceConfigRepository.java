package net.iofun.mdis.module.infra.dal.repository.db;

import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.CodegenColumnDO;
import net.iofun.mdis.module.infra.dal.dataobject.db.DataSourceConfigDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class DataSourceConfigRepository extends BaseRepository<DataSourceConfigDO, ObjectId> {
    public DataSourceConfigRepository(MongoTemplate mongoOperations) {
        super(DataSourceConfigDO.class, mongoOperations);
    }

    public List<DataSourceConfigDO> selectList() {
        return selectList(new LambdaQueryWrapperMongo<>(DataSourceConfigDO.class));
    }

    public DataSourceConfigDO selectById(String id) {
        LambdaQueryWrapperMongo<DataSourceConfigDO> queryWrapper = new LambdaQueryWrapperMongo<>(DataSourceConfigDO.class)
                .eqIfPresent(DataSourceConfigDO::getId, id);
        return findOne(queryWrapper).orElse(null);
    }

    public void deleteById(String id, LoginUser loginUser) {
        LambdaQueryWrapperMongo<DataSourceConfigDO> queryWrapper = new LambdaQueryWrapperMongo<>(DataSourceConfigDO.class)
                .eqIfPresent(DataSourceConfigDO::getId, id);
        softDeleteByQuery(queryWrapper, loginUser);
    }
}
