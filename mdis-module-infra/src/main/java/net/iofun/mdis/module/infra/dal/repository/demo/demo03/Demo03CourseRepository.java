package net.iofun.mdis.module.infra.dal.repository.demo.demo03;

import com.mongodb.client.result.UpdateResult;
import net.iofun.mdis.framework.common.pojo.PageParam;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.dal.dataobject.demo.demo03.Demo03CourseDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class Demo03CourseRepository extends BaseRepository<Demo03CourseDO, ObjectId> {
    public Demo03CourseRepository(MongoTemplate mongoOperations) {
        super(Demo03CourseDO.class, mongoOperations);
    }

    public PageResult<Demo03CourseDO> selectPage(PageParam reqVO, String studentId) {
        LambdaQueryWrapperMongo<Demo03CourseDO> queryWrapper = new LambdaQueryWrapperMongo<>(Demo03CourseDO.class)
                .eq(Demo03CourseDO::getStudentId, studentId)
                .orderByDesc(Demo03CourseDO::getId);
        return selectPage(reqVO, queryWrapper);
    }

    public List<Demo03CourseDO> selectListByStudentId(String studentId) {
        LambdaQueryWrapperMongo<Demo03CourseDO> queryWrapper = new LambdaQueryWrapperMongo<>(Demo03CourseDO.class)
                .eq(Demo03CourseDO::getStudentId, studentId);
        return selectList(queryWrapper);
    }

    public long deleteByStudentId(String studentId, LoginUser loginUser) {
        LambdaQueryWrapperMongo<Demo03CourseDO> queryWrapper = new LambdaQueryWrapperMongo<>(Demo03CourseDO.class)
                .eq(Demo03CourseDO::getStudentId, studentId);
        return softDeleteByQuery(queryWrapper, loginUser).getModifiedCount();
    }

    public List<Demo03CourseDO> insertBatch(List<Demo03CourseDO> importDOList, LoginUser loginUser) {
        return saveAll(importDOList, loginUser);
    }

    public long upsertDemo03Course(String studentId, Demo03CourseDO updateObj, LoginUser loginUser) {
        LambdaQueryWrapperMongo<Demo03CourseDO> queryWrapper = new LambdaQueryWrapperMongo<>(Demo03CourseDO.class)
                .eq(Demo03CourseDO::getStudentId, studentId);
        return upsert(queryWrapper, updateObj, loginUser).getModifiedCount();
    }

    public Demo03CourseDO selectById(String strId) {
        return selectById(new LambdaQueryWrapperMongo<>(Demo03CourseDO.class)
                .eq(Demo03CourseDO::getId, strId)).orElse(null);
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        return softDeleteByQuery(new LambdaQueryWrapperMongo<>(Demo03CourseDO.class)
                .eq(Demo03CourseDO::getId, id), loginUser);
    }
}
