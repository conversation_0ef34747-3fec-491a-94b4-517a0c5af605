package net.iofun.mdis.module.infra.framework.file.config;

import net.iofun.mdis.module.infra.framework.file.core.client.FileClientFactory;
import net.iofun.mdis.module.infra.framework.file.core.client.FileClientFactoryImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 文件配置类
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class MdisFileAutoConfiguration {

    @Bean
    public FileClientFactory fileClientFactory() {
        return new FileClientFactoryImpl();
    }

}
