package net.iofun.mdis.module.infra.dal.dataobject.codegen;

import net.iofun.mdis.framework.common.entity.BaseEntity;
import net.iofun.mdis.module.infra.enums.codegen.CodegenColumnHtmlTypeEnum;
import net.iofun.mdis.module.infra.enums.codegen.CodegenColumnListConditionEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 代码生成 column 字段定义
 *
 * <AUTHOR>
 */
@Document("infra_codegen_column")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CodegenColumnDO extends BaseEntity {

    /**
     * ID 编号
     */
//    @TableId
//    private Long id;
    /**
     * 表编号
     * <p>
     * 关联 {@link CodegenTableDO#getId()}
     */
    @Field("table_id")
    private String tableId;

    // ========== 表相关字段 ==========

    /**
     * 字段名
     *
     * 关联 {@link TableFieldMongo#getName()}
     */
    @Field("column_name")
    private String columnName;
    /**
     * 数据库字段类型
     *
     * 关联 {@link TableFieldMongo#getDataType()}
     */
    @Field("data_type")
    private String dataType;
    /**
     * 字段描述
     *
     * 关联 {@link TableFieldMongo#getComment()}
     */
    @Field("column_comment")
    private String columnComment;
    /**
     * 是否允许为空
     *
     * 关联 {@link TableFieldMongo#getNullable()} ()}
     */
    private Boolean nullable;
    /**
     * 是否主键
     *
     * 关联 {@link TableFieldMongo#getPrimaryKey()}
     */
    @Field("primary_key")
    private Boolean primaryKey;
    /**
     * 排序
     */
    @Field("ordinal_position")
    private Integer ordinalPosition;

    // ========== Java 相关字段 ==========

    /**
     * Java 属性类型
     *
     * 例如说 String、Boolean 等等
     *
     * 关联 {@link TableFieldMongo#getJavaType()}
     */
    @Field("java_type")
    private String javaType;
    /**
     * Java 属性名
     *
     * 关联 {@link TableFieldMongo#getJavaField()}
     */
    @Field("java_field")
    private String javaField;
    /**
     * 字典类型
     * <p>
     * 关联 DictTypeDO 的 type 属性
     */
    @Field("dict_type")
    private String dictType;
    /**
     * 数据示例，主要用于生成 Swagger 注解的 example 字段
     */
    private String example;

    // ========== CRUD 相关字段 ==========

    /**
     * 是否为 Create 创建操作的字段
     */
    @Field("create_operation")
    private Boolean createOperation;
    /**
     * 是否为 Update 更新操作的字段
     */
    @Field("update_operation")
    private Boolean updateOperation;
    /**
     * 是否为 List 查询操作的字段
     */
    @Field("list_operation")
    private Boolean listOperation;
    /**
     * List 查询操作的条件类型
     * <p>
     * 枚举 {@link CodegenColumnListConditionEnum}
     */
    @Field("list_operation_condition")
    private String listOperationCondition;
    /**
     * 是否为 List 查询操作的返回字段
     */
    @Field("list_operation_result")
    private Boolean listOperationResult;

    // ========== UI 相关字段 ==========

    /**
     * 显示类型
     * <p>
     * 枚举 {@link CodegenColumnHtmlTypeEnum}
     */
    @Field("html_type")
    private String htmlType;

}
