package net.iofun.mdis.module.infra.service.file;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.util.json.JsonUtils;
import net.iofun.mdis.framework.common.util.validation.ValidationUtils;
import net.iofun.mdis.module.infra.dal.repository.file.FileConfigRepository;
import net.iofun.mdis.module.infra.framework.file.core.client.FileClient;
import net.iofun.mdis.module.infra.framework.file.core.client.FileClientConfig;
import net.iofun.mdis.module.infra.framework.file.core.client.FileClientFactory;
import net.iofun.mdis.module.infra.framework.file.core.enums.FileStorageEnum;
import net.iofun.mdis.module.infra.controller.admin.file.vo.config.FileConfigPageReqVO;
import net.iofun.mdis.module.infra.controller.admin.file.vo.config.FileConfigSaveReqVO;
import net.iofun.mdis.module.infra.convert.file.FileConfigConvert;
import net.iofun.mdis.module.infra.dal.dataobject.file.FileConfigDO;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import jakarta.validation.Validator;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.framework.common.util.cache.CacheUtils.buildAsyncReloadingCache;
import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static net.iofun.mdis.module.infra.enums.ErrorCodeConstants.FILE_CONFIG_DELETE_FAIL_MASTER;
import static net.iofun.mdis.module.infra.enums.ErrorCodeConstants.FILE_CONFIG_NOT_EXISTS;

/**
 * 文件配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class FileConfigServiceImpl implements FileConfigService {

    private static final String CACHE_MASTER_ID = "0";

    /**
     * {@link FileClient} 缓存，通过它异步刷新 fileClientFactory
     */
    @Getter
    private final LoadingCache<String, FileClient> clientCache = buildAsyncReloadingCache(Duration.ofSeconds(10L),
            new CacheLoader<String, FileClient>() {

                @Override
                public FileClient load(String id) {
                    FileConfigDO config = Objects.equals(CACHE_MASTER_ID, id) ?
                            fileConfigRepository.selectByMaster() : fileConfigRepository.selectById(id);
                    if (config != null) {
                        fileClientFactory.createOrUpdateFileClient(config.getId().toHexString(), config.getStorage(), config.getClientConfig());
                    }
                    return fileClientFactory.getFileClient(null == config ? id : config.getId().toHexString());
                }

            });

    @Resource
    private FileClientFactory fileClientFactory;

    @Resource
    private FileConfigRepository fileConfigRepository;

    @Resource
    private Validator validator;

    @Override
    public String createFileConfig(FileConfigSaveReqVO createReqVO) {
        FileConfigDO fileConfig = FileConfigConvert.INSTANCE.convertFileConfigSaveReqVO2FileConfigDO(createReqVO)
                .setMaster(false); // 默认非 master
        // 设置配置
        fileConfig.setConfig(parseClientConfig(createReqVO.getStorage(), createReqVO.getConfig()));
        fileConfigRepository.insert(fileConfig, getLoginUser());
        return fileConfig.getId().toHexString();
    }

    @Override
    public void updateFileConfig(FileConfigSaveReqVO updateReqVO) {
        // 校验存在
        FileConfigDO config = validateFileConfigExists(updateReqVO.getId());
        // 更新
        FileConfigDO updateObj = FileConfigConvert.INSTANCE.convertFileConfigSaveReqVO2FileConfigDO(updateReqVO);
        // 设置配置
        JSONObject jsonObject = parseClientConfig(config.getStorage(), updateReqVO.getConfig());
        updateObj.setConfig(jsonObject);
        fileConfigRepository.updateById(updateObj, getLoginUser());

        // 清空缓存
        clearCache(config.getId().toHexString(), null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFileConfigMaster(String id) {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        validateFileConfigExists(oid);
        // 更新其它为非 master
        fileConfigRepository.updateBatch("master", false, getLoginUser());
        // 更新
        FileConfigDO fileConfigDO = new FileConfigDO();
        fileConfigDO.setId(oid);
        fileConfigDO.setMaster(true);
        fileConfigRepository.updateById(fileConfigDO, getLoginUser());

        // 清空缓存
        clearCache(null, true);
    }

//    private FileClientConfig parseClientConfig(Integer storage, Map<String, Object> config) {
    private JSONObject parseClientConfig(Integer storage, Map<String, Object> config) {
        // 获取配置类
        Class<? extends FileClientConfig> configClass = FileStorageEnum.getByStorage(storage)
                .getConfigClass();
        FileClientConfig clientConfig = JsonUtils.parseObject2(JsonUtils.toJsonString(config), configClass);
        // 参数校验
        ValidationUtils.validate(validator, clientConfig);
        // 设置参数
//        return clientConfig;
        // 返回 JSONObject
        return JSONObject.parseObject(JsonUtils.toJsonString(clientConfig));
    }

    @Override
    public void deleteFileConfig(String id) {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        FileConfigDO config = validateFileConfigExists(oid);
        if (Boolean.TRUE.equals(config.getMaster())) {
            throw exception(FILE_CONFIG_DELETE_FAIL_MASTER);
        }
        // 删除
        fileConfigRepository.deleteById(oid, getLoginUser());

        // 清空缓存
        clearCache(id, null);
    }

    /**
     * 清空指定文件配置
     *
     * @param id 配置编号
     * @param master 是否主配置
     */
    private void clearCache(String id, Boolean master) {
        if (id != null) {
            clientCache.invalidate(id);
        }
        if (Boolean.TRUE.equals(master)) {
            clientCache.invalidate(CACHE_MASTER_ID);
        }
    }

    private FileConfigDO validateFileConfigExists(ObjectId id) {
        FileConfigDO config = fileConfigRepository.selectById(id.toHexString());
        if (config == null) {
            throw exception(FILE_CONFIG_NOT_EXISTS);
        }
        return config;
    }

    @Override
    public FileConfigDO getFileConfig(String id) {
        return fileConfigRepository.selectById(id);
    }

    @Override
    public PageResult<FileConfigDO> getFileConfigPage(FileConfigPageReqVO pageReqVO) {
        return fileConfigRepository.selectPage(pageReqVO);
    }

    @Override
    public String testFileConfig(String id) throws Exception {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        validateFileConfigExists(oid);
        // 上传文件
        byte[] content = ResourceUtil.readBytes("file/erweima.jpg");
        return getFileClient(id).upload(content, IdUtil.fastSimpleUUID() + ".jpg", "image/jpeg");
    }

    @Override
    public FileClient getFileClient(String id) {
        try {
            return clientCache.getUnchecked(id);
        } catch (CacheLoader.InvalidCacheLoadException e) {
            log.error("[getFileClient][配置编号({}) 找不到客户端，将使用默认主配置]", id);
            // 返回主配置，而不是直接抛出异常
            try {
                return clientCache.getUnchecked(CACHE_MASTER_ID);
            } catch (Exception ex) {
                log.error("[getFileClient][主配置也找不到，返回null]", ex);
                return null;
            }
        }
    }

    @Override
    public FileClient getMasterFileClient() {
        return clientCache.getUnchecked(CACHE_MASTER_ID);
    }

}
