package net.iofun.mdis.module.infra.service.demo.demo01;

import net.iofun.mdis.module.infra.controller.admin.demo.demo01.vo.Demo01ContactPageReqVO;
import net.iofun.mdis.module.infra.controller.admin.demo.demo01.vo.Demo01ContactRespVO;
import net.iofun.mdis.module.infra.controller.admin.demo.demo01.vo.Demo01ContactSaveReqVO;
import net.iofun.mdis.module.infra.dal.repository.demo.demo01.Demo01ContactRepository;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import net.iofun.mdis.module.infra.dal.dataobject.demo.demo01.Demo01ContactDO;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.util.object.BeanUtils;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static net.iofun.mdis.module.infra.enums.ErrorCodeConstants.*;

/**
 * 示例联系人 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class Demo01ContactServiceImpl implements Demo01ContactService {

    @Resource
    private Demo01ContactRepository demo01ContactRepository;

    @Override
    public String createDemo01Contact(Demo01ContactSaveReqVO createReqVO) {
        // 插入
        Demo01ContactDO demo01Contact = BeanUtils.toBean(createReqVO, Demo01ContactDO.class);
        demo01ContactRepository.insert(demo01Contact, getLoginUser());
        // 返回
        return demo01Contact.getId().toString();
    }

    @Override
    public void updateDemo01Contact(Demo01ContactSaveReqVO updateReqVO) {
        // 校验存在
        validateDemo01ContactExists(updateReqVO.getId());
        // 更新
        Demo01ContactDO updateObj = BeanUtils.toBean(updateReqVO, Demo01ContactDO.class);
        demo01ContactRepository.updateById(updateObj, getLoginUser());
    }

    @Override
    public void deleteDemo01Contact(String id) {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        validateDemo01ContactExists(oid);
        // 删除
        demo01ContactRepository.deleteById(oid, getLoginUser());
    }

    private void validateDemo01ContactExists(ObjectId id) {
        if (!demo01ContactRepository.validateExists(id)) {
            throw exception(DEMO01_CONTACT_NOT_EXISTS);
        }
    }

    @Override
    public Demo01ContactDO getDemo01Contact(String id) {
        return demo01ContactRepository.selectById(id);
    }

    @Override
    public PageResult<Demo01ContactDO> getDemo01ContactPage(Demo01ContactPageReqVO pageReqVO) {
        return demo01ContactRepository.selectPage(pageReqVO);
    }

}