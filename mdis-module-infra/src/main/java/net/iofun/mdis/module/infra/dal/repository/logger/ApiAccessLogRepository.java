package net.iofun.mdis.module.infra.dal.repository.logger;

import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.controller.admin.logger.vo.apiaccesslog.ApiAccessLogPageReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.logger.ApiAccessLogDO;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Repository
public class ApiAccessLogRepository extends BaseRepository<ApiAccessLogDO, ObjectId> {
    public ApiAccessLogRepository(MongoTemplate mongoOperations) {
        super(ApiAccessLogDO.class, mongoOperations);
    }

    public PageResult<ApiAccessLogDO> selectPage(ApiAccessLogPageReqVO reqVO) {
        LambdaQueryWrapperMongo<ApiAccessLogDO> queryWrapper = new LambdaQueryWrapperMongo<>(ApiAccessLogDO.class)
                .eqIfPresent(ApiAccessLogDO::getUserId, reqVO.getUserId())
                .eqIfPresent(ApiAccessLogDO::getUserType, reqVO.getUserType())
                .eqIfPresent(ApiAccessLogDO::getApplicationName, reqVO.getApplicationName())
                .likeIfPresent(ApiAccessLogDO::getRequestUrl, reqVO.getRequestUrl())
                .betweenIfPresent(ApiAccessLogDO::getBeginTime, reqVO.getBeginTime())
                .gteIfPresent(ApiAccessLogDO::getDuration, reqVO.getDuration())
                .eqIfPresent(ApiAccessLogDO::getResultCode, reqVO.getResultCode())
                .orderByDesc(ApiAccessLogDO::getId);
        return selectPage(reqVO, queryWrapper);
    }

    /**
     * 物理删除指定时间之前的日志
     *
     * @param createTime 最大时间
     * @param limit 删除条数，防止一次删除太多
     * @return 删除条数
     */
    public Integer deleteByCreateTimeLt(LocalDateTime createTime, Integer limit) {
        LambdaQueryWrapperMongo<ApiAccessLogDO> queryWrapper = new LambdaQueryWrapperMongo<>(ApiAccessLogDO.class)
                .lteIfPresent(ApiAccessLogDO::getCreateTime, createTime)
                .limitIfPresent(limit);
        return (int)deleteByQuery(queryWrapper).getDeletedCount();
    }
}
