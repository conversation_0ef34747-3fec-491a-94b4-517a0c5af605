package net.iofun.mdis.module.infra.service.codegen;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.util.object.BeanUtils;
import net.iofun.mdis.module.infra.controller.admin.codegen.vo.CodegenCreateListReqVO;
import net.iofun.mdis.module.infra.controller.admin.codegen.vo.CodegenUpdateReqVO;
import net.iofun.mdis.module.infra.controller.admin.codegen.vo.column.CodegenColumnSaveReqVO;
import net.iofun.mdis.module.infra.controller.admin.codegen.vo.table.CodegenTablePageReqVO;
import net.iofun.mdis.module.infra.controller.admin.codegen.vo.table.CodegenTableSaveReqVO;
import net.iofun.mdis.module.infra.controller.admin.codegen.vo.table.DatabaseTableRespVO;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.CodegenColumnDO;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.CodegenTableDO;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.TableFieldMongo;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.TableInfoMongo;
import net.iofun.mdis.module.infra.dal.repository.codegen.CodegenColumnRepository;
import net.iofun.mdis.module.infra.dal.repository.codegen.CodegenTableRepository;
import net.iofun.mdis.module.infra.enums.codegen.CodegenSceneEnum;
import net.iofun.mdis.module.infra.enums.codegen.CodegenTemplateTypeEnum;
import net.iofun.mdis.module.infra.framework.codegen.config.CodegenProperties;
import net.iofun.mdis.module.infra.service.codegen.inner.CodegenBuilder;
import net.iofun.mdis.module.infra.service.codegen.inner.CodegenEngine;
import net.iofun.mdis.module.infra.service.db.DataSourceConfigService;
import net.iofun.mdis.module.infra.service.db.DatabaseTableService;
import com.google.common.annotations.VisibleForTesting;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.framework.common.util.collection.CollectionUtils.convertMap;
import static net.iofun.mdis.framework.common.util.collection.CollectionUtils.convertSet;
import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static net.iofun.mdis.module.infra.enums.ErrorCodeConstants.*;

/**
 * 代码生成 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class CodegenServiceImpl implements CodegenService {

    @Resource
    private DatabaseTableService databaseTableService;

    @Resource
    private CodegenTableRepository codegenTableRepository;
    @Resource
    private CodegenColumnRepository codegenColumnRepository;

    @Resource
    private CodegenBuilder codegenBuilder;
    @Resource
    private CodegenEngine codegenEngine;

    @Resource
    private CodegenProperties codegenProperties;

    @Resource
    private DataSourceConfigService dataSourceConfigService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> createCodegenList(String userNickName, CodegenCreateListReqVO reqVO) {
        List<String> ids = new ArrayList<>(reqVO.getTableNames().size());
        // 遍历添加。虽然效率会低一点，但是没必要做成完全批量，因为不会这么大量
        reqVO.getTableNames().forEach(tableName -> ids.add(createCodegen(userNickName, reqVO.getDataSourceConfigId(), tableName)));
        return ids;
    }

    @Override
    public String createCodegenTable(CodegenTableSaveReqVO reqVO) {
        // 新增一条空白 table 定义, 检查数据源是否存在
        if (dataSourceConfigService.getDataSourceConfig(reqVO.getDataSourceConfigId()) == null) {
            throw exception(CODEGEN_TABLE_NOT_EXISTS);
        }
        if (databaseTableService.getTable(reqVO.getDataSourceConfigId(), reqVO.getTableName()) != null) { // 表名称已经存在
            throw exception(CODEGEN_TABLE_EXISTS);
        }
        CodegenTableDO codegenTableDO = BeanUtils.toBean(reqVO, CodegenTableDO.class);
        codegenTableRepository.insert(codegenTableDO, getLoginUser());
        return codegenTableDO.getId().toHexString();
    }

    @Override
    public String createCodegenColumn(CodegenColumnSaveReqVO reqVO) {
        // 新增一条空白的字段信息, 只检查 table_id
        if (codegenTableRepository.selectById(reqVO.getTableId()) == null) {
            throw exception(CODEGEN_TABLE_NOT_EXISTS);
        }
        // 如果 dictType 是 null, 或者 undefined, 就改成空字符, 对应 codegen/vue3/views/index.vue.vm 前端模板中对 dictType 的判断
        if (null == reqVO.getDictType() || reqVO.getDictType().equals("undefined")) {
            reqVO.setDictType("");
        }
        CodegenColumnDO codegenColumnDO = BeanUtils.toBean(reqVO, CodegenColumnDO.class);
        codegenColumnRepository.insert(codegenColumnDO, getLoginUser());
        return codegenColumnDO.getId().toHexString();
    }

    private String createCodegen(String userNickName, String dataSourceConfigId, String tableName) {
        // 从数据库中，获得数据库表结构
        TableInfoMongo tableInfo = databaseTableService.getTable(dataSourceConfigId, tableName);
        // 导入
        return createCodegen0(userNickName, dataSourceConfigId, tableInfo);
    }

    private String createCodegen0(String userNickName, String dataSourceConfigId, TableInfoMongo tableInfo) {
        // 校验导入的表和字段非空
        validateTableInfo(tableInfo);
        // 校验是否已经存在
        if (codegenTableRepository.selectByTableNameAndDataSourceConfigId(tableInfo.getName(), dataSourceConfigId) != null) {
            throw exception(CODEGEN_TABLE_EXISTS);
        }

        // 构建 CodegenTableDO 对象，插入到 DB 中
        CodegenTableDO table = codegenBuilder.buildTable(tableInfo);
        table.setDataSourceConfigId(dataSourceConfigId);
        table.setScene(CodegenSceneEnum.ADMIN.getScene()); // 默认配置下，使用管理后台的模板
        table.setFrontType(codegenProperties.getFrontType());
        table.setAuthor(userNickName);
        codegenTableRepository.insert(table, getLoginUser());

        // 构建 CodegenColumnDO 数组，插入到 DB 中
        List<CodegenColumnDO> columns = codegenBuilder.buildColumns(table.getId().toHexString(), tableInfo.getFields());
        // 如果没有主键，则使用第一个字段作为主键
//        if (!tableInfo.isHavePrimaryKey()) {
//            columns.get(0).setPrimaryKey(true);
//        }
        codegenColumnRepository.saveAll(columns, getLoginUser());
        return table.getId().toHexString();
    }

    @VisibleForTesting
    void validateTableInfo(TableInfoMongo tableInfo) {
        if (tableInfo == null) {
            throw exception(CODEGEN_IMPORT_TABLE_NULL);
        }
//        if (StrUtil.isEmpty(tableInfo.getComment())) {
//            throw exception(CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL);
//        }
        if (CollUtil.isEmpty(tableInfo.getFields())) {
            throw exception(CODEGEN_IMPORT_COLUMNS_NULL);
        }
//        tableInfo.getFields().forEach(field -> {
//            if (StrUtil.isEmpty(field.getComment())) {
//                throw exception(CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL, field.getName());
//            }
//        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCodegen(CodegenUpdateReqVO updateReqVO) {
        // 校验是否已经存在
        if (codegenTableRepository.selectById(updateReqVO.getTable().getId().toHexString()) == null) {
            throw exception(CODEGEN_TABLE_NOT_EXISTS);
        }
        // 校验主表字段存在
        if (Objects.equals(updateReqVO.getTable().getTemplateType(), CodegenTemplateTypeEnum.SUB.getType())) { // 表的模板类型是子表-15的情况下
            if (codegenTableRepository.selectById(updateReqVO.getTable().getMasterTableId()) == null) { // 检查子表对应的主表是否存在
                throw exception(CODEGEN_MASTER_TABLE_NOT_EXISTS, updateReqVO.getTable().getMasterTableId());
            }
            if (CollUtil.findOne(updateReqVO.getColumns(),  // 子表关联主表的字段是否存在
                    column -> column.getId().equals(updateReqVO.getTable().getSubJoinColumnId())) == null) {
                throw exception(CODEGEN_SUB_COLUMN_NOT_EXISTS, updateReqVO.getTable().getSubJoinColumnId());
            }
        }

        // 更新 table 表定义
        CodegenTableDO updateTableObj = BeanUtils.toBean(updateReqVO.getTable(), CodegenTableDO.class);
        codegenTableRepository.updateById(updateTableObj, getLoginUser());
        // 更新 column 字段定义
        List<CodegenColumnDO> updateColumnObjs = BeanUtils.toBean(updateReqVO.getColumns(), CodegenColumnDO.class);
        updateColumnObjs.forEach(updateColumnObj -> codegenColumnRepository.updateById(updateColumnObj, getLoginUser()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncCodegenFromDB(String tableId) {
        // 校验是否已经存在
        CodegenTableDO table = codegenTableRepository.selectById(tableId);
        if (table == null) { // 如果数据库中的表, 还没被记录到表定义中, 就报错不存在
            throw exception(CODEGEN_TABLE_NOT_EXISTS);
        }
        // 从数据库中，获得数据库表结构
        TableInfoMongo tableInfo = databaseTableService.getTable(table.getDataSourceConfigId(), table.getTableName());
        // 执行同步
        syncCodegen0(tableId, tableInfo);
    }

    private void syncCodegen0(String tableId, TableInfoMongo tableInfo) {
        // 1. 校验导入的表和字段非空
        validateTableInfo(tableInfo);
        List<TableFieldMongo> tableFields = tableInfo.getFields();

        // 2. 构建 CodegenColumnDO 数组，只同步新增的字段
        List<CodegenColumnDO> codegenColumns = codegenColumnRepository.selectListByTableId(tableId);
        Set<String> codegenColumnNames = convertSet(codegenColumns, CodegenColumnDO::getColumnName);

        // 3.1 计算需要【修改】的字段，插入时重新插入，删除时将原来的删除
        Map<String, CodegenColumnDO> codegenColumnDOMap = convertMap(codegenColumns, CodegenColumnDO::getColumnName);
        BiPredicate<TableFieldMongo, CodegenColumnDO> primaryKeyPredicate =
                (tableField, codegenColumn) -> tableField.getDataType().equals(codegenColumn.getDataType())
                        && tableField.getJavaType().equals(codegenColumn.getJavaType());
        Set<String> modifyFieldNames = tableFields.stream() // 字段名称相同, 但是物理类型或者 Java 类型不同的, 则修改
                .filter(tableField -> codegenColumnDOMap.get(tableField.getName()) != null
                        && !primaryKeyPredicate.test(tableField, codegenColumnDOMap.get(tableField.getName())))
                .map(TableFieldMongo::getName)
                .collect(Collectors.toSet());
        // 3.2 计算需要【删除】的字段
        Set<String> tableFieldNames = convertSet(tableFields, TableFieldMongo::getName);
        Set<String> deleteColumnIds = codegenColumns.stream() // 字段定义表中的名称在物理表中不存在, 或者是在修改集合中的, 则删除
                .filter(column -> (!tableFieldNames.contains(column.getColumnName())) || modifyFieldNames.contains(column.getColumnName()))
                .map(codegenColumnDO->codegenColumnDO.getId().toHexString()).collect(Collectors.toSet());
        // 移除已经存在且不需要修改的字段(字段定义表和物理表的字段中都有并且不需要修改的), 剩下的就是新增的
        tableFields.removeIf(column -> codegenColumnNames.contains(column.getName()) && (!modifyFieldNames.contains(column.getName())));
        if (CollUtil.isEmpty(tableFields) && CollUtil.isEmpty(deleteColumnIds)) { // 没有新增的, 且没有需要删除的, 就不动
            throw exception(CODEGEN_SYNC_NONE_CHANGE);
        }

        // 4.1 插入新增的字段, 修改的也是先删除再插入
        List<CodegenColumnDO> columns = codegenBuilder.buildColumns(tableId, tableFields);
        codegenColumnRepository.saveAll(columns, getLoginUser());
        // 4.2 删除不存在的字段
        if (CollUtil.isNotEmpty(deleteColumnIds)) {
            codegenColumnRepository.deleteBatchIds(deleteColumnIds);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCodegen(String tableId) {
        // 校验是否已经存在
        if (codegenTableRepository.selectById(tableId) == null) {
            throw exception(CODEGEN_TABLE_NOT_EXISTS);
        }

        // 删除 table 表定义
        codegenTableRepository.deleteById(tableId);
        // 删除 column 字段定义
        codegenColumnRepository.deleteListByTableId(tableId);
    }

    @Override
    public void deleteCodegenColumn(String columnId) {
        // 校验是否已经存在
        if (codegenColumnRepository.selectById(columnId) == null) {
            throw exception(CODEGEN_TABLE_NOT_EXISTS);
        }

        // 删除 column 字段定义
        codegenColumnRepository.deleteById(columnId);
    }

    @Override
    public List<CodegenTableDO> getCodegenTableList(String dataSourceConfigId) {
        return codegenTableRepository.selectListByDataSourceConfigId(dataSourceConfigId);
    }

    @Override
    public PageResult<CodegenTableDO> getCodegenTablePage(CodegenTablePageReqVO pageReqVO) {
        return codegenTableRepository.selectPage(pageReqVO);
    }

    @Override
    public CodegenTableDO getCodegenTable(String id) {
        return codegenTableRepository.selectById(id);
    }

    @Override
    public List<CodegenColumnDO> getCodegenColumnListByTableId(String tableId) {
        return codegenColumnRepository.selectListByTableId(tableId);
    }

    @Override
    public Map<String, String> generationCodes(String tableId) {
        // 校验是否已经存在
        CodegenTableDO table = codegenTableRepository.selectById(tableId);
        if (table == null) {
            throw exception(CODEGEN_TABLE_NOT_EXISTS);
        }
        List<CodegenColumnDO> columns = codegenColumnRepository.selectListByTableId(tableId);
        if (CollUtil.isEmpty(columns)) {
            throw exception(CODEGEN_COLUMN_NOT_EXISTS);
        }

        // 如果是主子表，则加载对应的子表信息
        List<CodegenTableDO> subTables = null;
        List<List<CodegenColumnDO>> subColumnsList = null;
        if (CodegenTemplateTypeEnum.isMaster(table.getTemplateType())) {
            // 校验子表存在
            subTables = codegenTableRepository.selectListByTemplateTypeAndMasterTableId(
                    CodegenTemplateTypeEnum.SUB.getType(), tableId);
            if (CollUtil.isEmpty(subTables)) {
                throw exception(CODEGEN_MASTER_GENERATION_FAIL_NO_SUB_TABLE);
            }
            // 校验子表的关联字段存在
            subColumnsList = new ArrayList<>();
            for (CodegenTableDO subTable : subTables) {
                List<CodegenColumnDO> subColumns = codegenColumnRepository.selectListByTableId(subTable.getId().toHexString());
                if (CollUtil.findOne(subColumns, column -> column.getId().toHexString().equals(subTable.getSubJoinColumnId())) == null) {
                    throw exception(CODEGEN_SUB_COLUMN_NOT_EXISTS, subTable.getId());
                }
                subColumnsList.add(subColumns);
            }
        }

        // 执行生成
        return codegenEngine.execute(table, columns, subTables, subColumnsList);
    }

    @Override
    public List<DatabaseTableRespVO> getDatabaseTableList(String dataSourceConfigId, String name, String comment) {
        List<TableInfoMongo> tables = databaseTableService.getTableList(dataSourceConfigId, name, comment);
        // 移除在 Codegen 中，已经存在的
        Set<String> existsTables = convertSet(
                codegenTableRepository.selectListByDataSourceConfigId(dataSourceConfigId), CodegenTableDO::getTableName);
        tables.removeIf(table -> existsTables.contains(table.getName()));
        return BeanUtils.toBean(tables, DatabaseTableRespVO.class);
    }

}
