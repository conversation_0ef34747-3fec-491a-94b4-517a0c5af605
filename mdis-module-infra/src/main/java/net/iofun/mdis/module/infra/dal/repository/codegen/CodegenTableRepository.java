package net.iofun.mdis.module.infra.dal.repository.codegen;

import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.controller.admin.codegen.vo.table.CodegenTablePageReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.CodegenTableDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class CodegenTableRepository extends BaseRepository<CodegenTableDO, ObjectId> {
    public CodegenTableRepository(MongoTemplate mongoOperations) {
        super(CodegenTableDO.class, mongoOperations);
    }
    
    public CodegenTableDO selectByTableNameAndDataSourceConfigId(String tableName, String dataSourceConfigId) {
        LambdaQueryWrapperMongo<CodegenTableDO> queryWrapper = new LambdaQueryWrapperMongo<>(CodegenTableDO.class)
                .eqIfPresent(CodegenTableDO::getTableName, tableName)
                .eqIfPresent(CodegenTableDO::getDataSourceConfigId, dataSourceConfigId);
        return findOne(queryWrapper).orElse(null);
    }

    public PageResult<CodegenTableDO> selectPage(CodegenTablePageReqVO pageReqVO) {
        LambdaQueryWrapperMongo<CodegenTableDO> queryWrapper = new LambdaQueryWrapperMongo<>(CodegenTableDO.class)
                .likeIfPresent(CodegenTableDO::getTableName, pageReqVO.getTableName())
                .likeIfPresent(CodegenTableDO::getTableComment, pageReqVO.getTableComment())
                .likeIfPresent(CodegenTableDO::getClassName, pageReqVO.getClassName())
                .betweenIfPresent(CodegenTableDO::getCreateTime, pageReqVO.getCreateTime())
                .orderByDesc(CodegenTableDO::getUpdateTime);
        return selectPage(pageReqVO, queryWrapper);
    }

    public List<CodegenTableDO> selectListByDataSourceConfigId(String dataSourceConfigId) {
        LambdaQueryWrapperMongo<CodegenTableDO> queryWrapper = new LambdaQueryWrapperMongo<>(CodegenTableDO.class)
                .eqIfPresent(CodegenTableDO::getDataSourceConfigId, dataSourceConfigId);
        return selectList(queryWrapper);
    }

    public List<CodegenTableDO> selectListByTemplateTypeAndMasterTableId(Integer templateType, String masterTableId) {
        LambdaQueryWrapperMongo<CodegenTableDO> queryWrapper = new LambdaQueryWrapperMongo<>(CodegenTableDO.class)
                .eqIfPresent(CodegenTableDO::getTemplateType, templateType)
                .eqIfPresent(CodegenTableDO::getMasterTableId, masterTableId);
        return selectList(queryWrapper);
    }

    public CodegenTableDO selectById(String id) {
        LambdaQueryWrapperMongo<CodegenTableDO> queryWrapper = new LambdaQueryWrapperMongo<>(CodegenTableDO.class)
                .eq(CodegenTableDO::getId, id);
        return findOne(queryWrapper).orElse(null);
    }

    public long deleteById(String id) {
        LambdaQueryWrapperMongo<CodegenTableDO> queryWrapper = new LambdaQueryWrapperMongo<>(CodegenTableDO.class)
                .eq(CodegenTableDO::getId, id);
        return deleteByQuery(queryWrapper).getDeletedCount();
    }
    
}
