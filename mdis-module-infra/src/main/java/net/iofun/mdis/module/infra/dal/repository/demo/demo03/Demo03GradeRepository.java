package net.iofun.mdis.module.infra.dal.repository.demo.demo03;

import com.mongodb.client.result.UpdateResult;
import net.iofun.mdis.framework.common.pojo.PageParam;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.dal.dataobject.demo.demo03.Demo03GradeDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class Demo03GradeRepository extends BaseRepository<Demo03GradeDO, ObjectId> {
    public Demo03GradeRepository(MongoTemplate mongoOperations) {
        super(Demo03GradeDO.class, mongoOperations);
    }

    public PageResult<Demo03GradeDO> selectPage(PageParam reqVO, String studentId) {
        LambdaQueryWrapperMongo<Demo03GradeDO> queryWrapper = new LambdaQueryWrapperMongo<>(Demo03GradeDO.class)
                .eq(Demo03GradeDO::getStudentId, studentId)
                .orderByDesc(Demo03GradeDO::getId);
        return selectPage(reqVO, queryWrapper);
    }

    public Demo03GradeDO selectByStudentId(String studentId) {
        LambdaQueryWrapperMongo<Demo03GradeDO> queryWrapper = new LambdaQueryWrapperMongo<>(Demo03GradeDO.class)
                .eq(Demo03GradeDO::getStudentId, studentId);
        return findOne(queryWrapper).orElse(null);
    }

    public long deleteByStudentId(String studentId, LoginUser loginUser) {
        LambdaQueryWrapperMongo<Demo03GradeDO> queryWrapper = new LambdaQueryWrapperMongo<>(Demo03GradeDO.class)
                .eq(Demo03GradeDO::getStudentId, studentId);
        return softDeleteByQuery(queryWrapper, loginUser).getModifiedCount();
    }

    public long insertOrUpdate(Demo03GradeDO demo03Grade, LoginUser loginUser) {
        LambdaQueryWrapperMongo<Demo03GradeDO> queryWrapper = new LambdaQueryWrapperMongo<>(Demo03GradeDO.class)
                .eq(Demo03GradeDO::getStudentId, demo03Grade.getStudentId());
        return upsert(queryWrapper, demo03Grade, loginUser).getModifiedCount();
    }

    public Demo03GradeDO selectById(String strId) {
        return selectById(new LambdaQueryWrapperMongo<>(Demo03GradeDO.class)
                .eq(Demo03GradeDO::getId, strId)).orElse(null);
    }

    public boolean validateExists(ObjectId id) {
        return existsByQuery(new LambdaQueryWrapperMongo<>(Demo03GradeDO.class)
                .eq(Demo03GradeDO::getId, id));
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        return softDeleteByQuery(new LambdaQueryWrapperMongo<>(Demo03GradeDO.class)
                .eq(Demo03GradeDO::getId, id), loginUser);
    }
}
