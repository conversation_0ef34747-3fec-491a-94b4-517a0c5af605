package net.iofun.mdis.module.infra.service.logger;

import cn.hutool.core.util.StrUtil;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.util.object.BeanUtils;
import net.iofun.mdis.framework.tenant.core.context.TenantContextHolder;
import net.iofun.mdis.framework.tenant.core.util.TenantUtils;
import net.iofun.mdis.framework.common.biz.infra.logger.dto.ApiErrorLogCreateReqDTO;
import net.iofun.mdis.module.infra.controller.admin.logger.vo.apierrorlog.ApiErrorLogPageReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.logger.ApiErrorLogDO;
import net.iofun.mdis.module.infra.dal.repository.logger.ApiErrorLogRepository;
import net.iofun.mdis.module.infra.enums.logger.ApiErrorLogProcessStatusEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static net.iofun.mdis.module.infra.dal.dataobject.logger.ApiErrorLogDO.REQUEST_PARAMS_MAX_LENGTH;
import static net.iofun.mdis.module.infra.enums.ErrorCodeConstants.API_ERROR_LOG_NOT_FOUND;
import static net.iofun.mdis.module.infra.enums.ErrorCodeConstants.API_ERROR_LOG_PROCESSED;

/**
 * API 错误日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ApiErrorLogServiceImpl implements ApiErrorLogService {

    @Resource
    private ApiErrorLogRepository apiErrorLogRepository;

    @Override
    public void createApiErrorLog(ApiErrorLogCreateReqDTO createDTO) {
        ApiErrorLogDO apiErrorLog = BeanUtils.toBean(createDTO, ApiErrorLogDO.class)
                .setProcessStatus(ApiErrorLogProcessStatusEnum.INIT.getStatus());
        apiErrorLog.setRequestParams(StrUtil.maxLength(apiErrorLog.getRequestParams(), REQUEST_PARAMS_MAX_LENGTH));
        if (TenantContextHolder.getTenantId() != null) {
            apiErrorLogRepository.insert(apiErrorLog, getLoginUser());
        } else {
            // 极端情况下，上下文中没有租户时，此时忽略租户上下文，避免插入失败！
            TenantUtils.executeIgnore(() -> apiErrorLogRepository.insert(apiErrorLog, getLoginUser()));
        }
    }

    @Override
    public PageResult<ApiErrorLogDO> getApiErrorLogPage(ApiErrorLogPageReqVO pageReqVO) {
        return apiErrorLogRepository.selectPage(pageReqVO);
    }

    @Override
    public void updateApiErrorLogProcess(String id, Integer processStatus, String processUserId) {
        ApiErrorLogDO errorLog = apiErrorLogRepository.selectById(id);
        if (errorLog == null) {
            throw exception(API_ERROR_LOG_NOT_FOUND);
        }
        if (!ApiErrorLogProcessStatusEnum.INIT.getStatus().equals(errorLog.getProcessStatus())) {
            throw exception(API_ERROR_LOG_PROCESSED);
        }
        // 标记处理
        ApiErrorLogDO updateObj = new ApiErrorLogDO();
        updateObj.setId(new ObjectId(id));
        updateObj.setProcessStatus(processStatus);
        updateObj.setProcessUserId(processUserId);
        updateObj.setProcessTime(LocalDateTime.now());
        apiErrorLogRepository.updateById(updateObj, getLoginUser());
    }

    @Override
    @SuppressWarnings("DuplicatedCode")
    public Integer cleanErrorLog(Integer exceedDay, Integer deleteLimit) {
        int count = 0;
        LocalDateTime expireDate = LocalDateTime.now().minusDays(exceedDay);
        // 循环删除，直到没有满足条件的数据
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            int deleteCount = apiErrorLogRepository.deleteByCreateTimeLt(expireDate, deleteLimit);
            count += deleteCount;
            // 达到删除预期条数，说明到底了
            if (deleteCount < deleteLimit) {
                break;
            }
        }
        return count;
    }

}
