package net.iofun.mdis.module.infra.dal.dataobject.logger;

import net.iofun.mdis.framework.common.enums.UserTypeEnum;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import net.iofun.mdis.module.infra.enums.logger.ApiErrorLogProcessStatusEnum;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

/**
 * API 异常数据
 *
 * <AUTHOR>
 */
@Document("infra_api_error_log")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiErrorLogDO extends BaseEntity {

    /**
     * {@link #requestParams} 的最大长度
     */
    public static final Integer REQUEST_PARAMS_MAX_LENGTH = 8000;

    /**
     * 编号
     */
//    @TableId
//    private Long id;
    /**
     * 用户编号
     */
    @Field("user_id")
    private String userId;
    /**
     * 链路追踪编号
     *
     * 一般来说，通过链路追踪编号，可以将访问日志，错误日志，链路追踪日志，logger 打印日志等，结合在一起，从而进行排错。
     */
    @Field("trace_id")
    private String traceId;
    /**
     * 用户类型
     *
     * 枚举 {@link UserTypeEnum}
     */
    @Field("user_type")
    private Integer userType;
    /**
     * 应用名
     *
     * 目前读取 META-INF.application.name
     */
    @Field("application_name")
    private String applicationName;

    // ========== 请求相关字段 ==========

    /**
     * 请求方法名
     */
    @Field("request_method")
    private String requestMethod;
    /**
     * 访问地址
     */
    @Field("request_url")
    private String requestUrl;
    /**
     * 请求参数
     *
     * query: Query String
     * body: Quest Body
     */
    @Field("request_params")
    private String requestParams;
    /**
     * 用户 IP
     */
    @Field("user_ip")
    private String userIp;
    /**
     * 浏览器 UA
     */
    @Field("user_agent")
    private String userAgent;

    // ========== 异常相关字段 ==========

    /**
     * 异常发生时间
     */
    @Field("exception_time")
    private LocalDateTime exceptionTime;
    /**
     * 异常名
     *
     * {@link Throwable#getClass()} 的类全名
     */
    @Field("exception_name")
    private String exceptionName;
    /**
     * 异常导致的消息
     *
     * {@link cn.hutool.core.exceptions.ExceptionUtil#getMessage(Throwable)}
     */
    @Field("exception_message")
    private String exceptionMessage;
    /**
     * 异常导致的根消息
     *
     * {@link cn.hutool.core.exceptions.ExceptionUtil#getRootCauseMessage(Throwable)}
     */
    @Field("exception_root_cause_message")
    private String exceptionRootCauseMessage;
    /**
     * 异常的栈轨迹
     *
     * {@link org.apache.commons.lang3.exception.ExceptionUtils#getStackTrace(Throwable)}
     */
    @Field("exception_stack_trace")
    private String exceptionStackTrace;
    /**
     * 异常发生的类全名
     *
     * {@link StackTraceElement#getClassName()}
     */
    @Field("exception_class_name")
    private String exceptionClassName;
    /**
     * 异常发生的类文件
     *
     * {@link StackTraceElement#getFileName()}
     */
    @Field("exception_file_name")
    private String exceptionFileName;
    /**
     * 异常发生的方法名
     *
     * {@link StackTraceElement#getMethodName()}
     */
    @Field("exception_method_name")
    private String exceptionMethodName;
    /**
     * 异常发生的方法所在行
     *
     * {@link StackTraceElement#getLineNumber()}
     */
    @Field("exception_line_number")
    private Integer exceptionLineNumber;

    // ========== 处理相关字段 ==========

    /**
     * 处理状态
     *
     * 枚举 {@link ApiErrorLogProcessStatusEnum}
     */
    @Field("process_status")
    private Integer processStatus;
    /**
     * 处理时间
     */
    @Field("process_time")
    private LocalDateTime processTime;
    /**
     * 处理用户编号
     *
     * 关联 net.iofun.mdis.adminserver.modules.system.dal.dataobject.user.SysUserDO.SysUserDO#getId()
     */
    @Field("process_user_id")
    private String processUserId;

}
