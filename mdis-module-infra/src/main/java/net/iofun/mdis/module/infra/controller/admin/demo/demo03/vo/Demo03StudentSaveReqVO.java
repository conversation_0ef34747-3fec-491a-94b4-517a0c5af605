package net.iofun.mdis.module.infra.controller.admin.demo.demo03.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.time.LocalDateTime;

import net.iofun.mdis.framework.common.vo.BaseVO;
import net.iofun.mdis.module.infra.dal.dataobject.demo.demo03.Demo03CourseDO;
import net.iofun.mdis.module.infra.dal.dataobject.demo.demo03.Demo03GradeDO;

@Schema(description = "管理后台 - 学生新增/修改 Request VO")
@Data
public class Demo03StudentSaveReqVO extends BaseVO {

    @Schema(description = "名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "名字不能为空")
    private String name;

    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "性别不能为空")
    private Integer sex;

    @Schema(description = "出生日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "出生日期不能为空")
    private LocalDateTime birthday;

    @Schema(description = "简介", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @NotEmpty(message = "简介不能为空")
    private String description;


    private List<Demo03CourseDO> demo03Courses;

    private Demo03GradeDO demo03Grade;

}