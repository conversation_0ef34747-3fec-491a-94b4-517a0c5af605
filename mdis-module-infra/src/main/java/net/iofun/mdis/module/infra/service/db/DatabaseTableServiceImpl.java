package net.iofun.mdis.module.infra.service.db;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.mongodb.client.MongoClient;
import lombok.extern.slf4j.Slf4j;
import net.iofun.mdis.framework.mongo.core.util.MongoUtils;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.TableInfoMongo;
import net.iofun.mdis.module.infra.dal.dataobject.db.DataSourceConfigDO;

import jakarta.annotation.Resource;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.module.infra.enums.ErrorCodeConstants.*;

/**
 * 数据库表 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DatabaseTableServiceImpl implements DatabaseTableService {

    @Resource
    private DataSourceConfigService dataSourceConfigService;
    
    @Resource(name = "mongoTemplate")
    private MongoTemplate mongoTemplate;
    
    @Override
    public List<TableInfoMongo> getTableList(String dataSourceConfigId, String nameLike, String commentLike) {

        List<String> tableNames;
        DataSourceConfigDO dataSourceConfig = dataSourceConfigService.getDataSourceConfig(dataSourceConfigId);
        String mongoUri = dataSourceConfig.getUrl();
        if (dataSourceConfigId.equals(DataSourceConfigDO.ID_MASTER)) {
            // 默认的 MongoDB 数据库, new ArrayList 确保是一个可变集合, 否则不能使用下面的 removeIf
            tableNames = new ArrayList<>(mongoTemplate.getCollectionNames());
        } else {
            tableNames = dataSourceConfigService.getMongoDBTableList(mongoUri);
        }
        tableNames.removeIf(table -> table.matches("ACT_[\\S\\s]+|QRTZ_[\\S\\s]+|FLW_[\\S\\s]+"));// 移除工作流和定时任务前缀的表名
        List<String> tableNames2 = tableNames.stream().filter(table -> StrUtil.isEmpty(nameLike) || table.contains(nameLike)).collect(Collectors.toList());
        return dataSourceConfigService.getMongoDBTableInfoList(mongoUri, tableNames2);
    }

    @Override
    public TableInfoMongo getTable(String dataSourceConfigId, String tableName) {
        if (DataSourceConfigDO.ID_MASTER.equals(dataSourceConfigId)) { // 从默认的 master 数据库获取
            return dataSourceConfigService.getTableInfo(mongoTemplate, tableName);
        } else {
            String mongoUri = dataSourceConfigService.getDataSourceConfig(dataSourceConfigId).getUrl();
            try (MongoClient mongoClient = MongoUtils.createMongoClient(mongoUri)) { // 从其它数据库获取
            String dbName = MongoUtils.extractDatabaseName(mongoUri);
            Assert.notNull(dbName, "MongoDB database name cannot be empty!");
            return dataSourceConfigService.getTableInfo(mongoClient, dbName, tableName);
            } catch (Exception ex) {
                throw exception(DATA_SOURCE_CONFIG_MONGO_GET_TABLES_INFO_FAIL, ex.getMessage());
            }
        }
    }

}
