package net.iofun.mdis.module.infra.dal.dataobject.codegen;

import net.iofun.mdis.framework.common.entity.BaseEntity;
import net.iofun.mdis.module.infra.dal.dataobject.db.DataSourceConfigDO;
import net.iofun.mdis.module.infra.enums.codegen.CodegenFrontTypeEnum;
import net.iofun.mdis.module.infra.enums.codegen.CodegenSceneEnum;
import net.iofun.mdis.module.infra.enums.codegen.CodegenTemplateTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 代码生成 table 表定义
 *
 * <AUTHOR>
 */
@Document("infra_codegen_table")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CodegenTableDO extends BaseEntity {

    /**
     * ID 编号
     */
//    @TableId
//    private Long id;

    /**
     * 数据源编号
     *
     * 关联 {@link DataSourceConfigDO#getId()}
     */
    @Field("data_source_config_id")
    private String dataSourceConfigId;
    /**
     * 生成场景
     *
     * 枚举 {@link CodegenSceneEnum}
     */
    private Integer scene;

    // ========== 表相关字段 ==========

    /**
     * 表名称
     *
     * 关联 {@link TableInfoMongo#getName()}
     */
    @Field("table_name")
    private String tableName;
    /**
     * 表描述
     *
     * 关联 {@link TableInfoMongo#getComment()}
     */
    @Field("table_comment")
    private String tableComment;
    /**
     * 备注
     */
    private String remark;

    // ========== 类相关字段 ==========

    /**
     * 模块名，即一级目录
     *
     * 例如说，system、infra、tool 等等
     */
    @Field("module_name")
    private String moduleName;
    /**
     * 业务名，即二级目录
     *
     * 例如说，user、permission、dict 等等
     */
    @Field("business_name")
    private String businessName;
    /**
     * 类名称（首字母大写）
     *
     * 例如说，SysUser、SysMenu、SysDictData 等等
     */
    @Field("class_name")
    private String className;
    /**
     * 类描述
     */
    @Field("class_comment")
    private String classComment;
    /**
     * 作者
     */
    private String author;

    // ========== 生成相关字段 ==========

    /**
     * 模板类型
     *
     * 枚举 {@link CodegenTemplateTypeEnum}
     */
    @Field("template_type")
    private Integer templateType;
    /**
     * 代码生成的前端类型
     *
     * 枚举 {@link CodegenFrontTypeEnum}
     */
    @Field("front_type")
    private Integer frontType;

    // ========== 菜单相关字段 ==========

    /**
     * 父菜单编号
     *
     * 关联 MenuDO 的 id 属性
     */
    @Field("parent_menu_id")
    private String parentMenuId;

    // ========== 主子表相关字段 ==========

    /**
     * 主表的编号
     *
     * 关联 {@link CodegenTableDO#getId()}
     */
    @Field("master_table_id")
    private String masterTableId;
    /**
     * 【自己】子表关联主表的字段编号
     *
     * 关联 {@link CodegenColumnDO#getId()}
     */
    @Field("sub_join_column_id")
    private String subJoinColumnId;
    /**
     * 主表与子表是否一对多
     *
     * true：一对多
     * false：一对一
     */
    @Field("sub_join_many")
    private Boolean subJoinMany;

    // ========== 树表相关字段 ==========

    /**
     * 树表的父字段编号
     *
     * 关联 {@link CodegenColumnDO#getId()}
     */
    @Field("tree_parent_column_id")
    private String treeParentColumnId;
    /**
     * 树表的名字字段编号
     *
     * 名字的用途：新增或修改时，select 框展示的字段
     *
     * 关联 {@link CodegenColumnDO#getId()}
     */
    @Field("tree_name_column_id")
    private String treeNameColumnId;

}
