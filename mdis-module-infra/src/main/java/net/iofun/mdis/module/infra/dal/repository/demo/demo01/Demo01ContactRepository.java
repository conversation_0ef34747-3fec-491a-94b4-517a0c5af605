package net.iofun.mdis.module.infra.dal.repository.demo.demo01;

import com.mongodb.client.result.UpdateResult;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.module.infra.controller.admin.demo.demo01.vo.Demo01ContactPageReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.demo.demo01.Demo01ContactDO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class Demo01ContactRepository extends BaseRepository<Demo01ContactDO, ObjectId> {
    public Demo01ContactRepository(MongoTemplate mongoOperations) {
        super(Demo01ContactDO.class, mongoOperations);
    }

    public PageResult<Demo01ContactDO> selectPage(Demo01ContactPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperMongo<>(Demo01ContactDO.class)
                .likeIfPresent(Demo01ContactDO::getName, reqVO.getName())
                .eqIfPresent(Demo01ContactDO::getSex, reqVO.getSex())
                .betweenIfPresent(Demo01ContactDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(Demo01ContactDO::getId));
    }

    public Demo01ContactDO selectById(String strId) {
        return selectById(new LambdaQueryWrapperMongo<>(Demo01ContactDO.class)
                .eq(Demo01ContactDO::getId, strId)).orElse(null);
    }

    public boolean validateExists(ObjectId id) {
        return existsByQuery(new LambdaQueryWrapperMongo<>(Demo01ContactDO.class)
                .eq(Demo01ContactDO::getId, id));
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        return softDeleteByQuery(new LambdaQueryWrapperMongo<>(Demo01ContactDO.class)
                .eq(Demo01ContactDO::getId, id), loginUser);
    }
}
