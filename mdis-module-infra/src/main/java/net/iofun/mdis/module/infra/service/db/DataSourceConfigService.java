package net.iofun.mdis.module.infra.service.db;

import com.mongodb.client.MongoClient;
import net.iofun.mdis.module.infra.controller.admin.db.vo.DataSourceConfigSaveReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.codegen.TableInfoMongo;
import net.iofun.mdis.module.infra.dal.dataobject.db.DataSourceConfigDO;

import jakarta.validation.Valid;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.List;

/**
 * 数据源配置 Service 接口
 *
 * <AUTHOR>
 */
public interface DataSourceConfigService {

    /**
     * 创建数据源配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDataSourceConfig(@Valid DataSourceConfigSaveReqVO createReqVO);

    /**
     * 更新数据源配置
     *
     * @param updateReqVO 更新信息
     */
    void updateDataSourceConfig(@Valid DataSourceConfigSaveReqVO updateReqVO);

    /**
     * 删除数据源配置
     *
     * @param id 编号
     */
    void deleteDataSourceConfig(String id);

    /**
     * 获得数据源配置
     *
     * @param id 编号
     * @return 数据源配置
     */
    DataSourceConfigDO getDataSourceConfig(String id);

    /**
     * 获得数据源配置列表
     *
     * @return 数据源配置列表
     */
    List<DataSourceConfigDO> getDataSourceConfigList();

    List<String> getMongoDBTableList(String mongoUri);

    List<TableInfoMongo> getMongoDBTableInfoList(String mongoUri, List<String> tableNames);

    TableInfoMongo getTableInfo(MongoTemplate mongoTemplate, String tableName);

    TableInfoMongo getTableInfo(MongoClient mongoClient, String dbName, String tableName);
}
