package net.iofun.mdis.module.infra.dal.dataobject.demo.demo02;

import net.iofun.mdis.framework.common.entity.BaseEntity;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 示例分类 DO
 *
 * <AUTHOR>
 */
@Document("mdis_demo02_category")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Demo02CategoryDO extends BaseEntity {

    public static final String PARENT_ID_ROOT = "0";

    /**
     * 编号
     */
//    @TableId
//    private Long id;
    /**
     * 名字
     */
    private String name;
    /**
     * 父级编号
     */
    @Field("parent_id")
    private String parentId;

}