package net.iofun.mdis.module.infra.service.job;

import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.module.infra.controller.admin.job.vo.log.JobLogPageReqVO;
import net.iofun.mdis.module.infra.dal.dataobject.job.JobLogDO;
import net.iofun.mdis.module.infra.dal.repository.job.JobLogRepository;
import net.iofun.mdis.module.infra.enums.job.JobLogStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;

import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;

/**
 * Job 日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class JobLogServiceImpl implements JobLogService {

    @Resource
    private JobLogRepository jobLogRepository;

    @Override
    public String createJobLog(String jobId, LocalDateTime beginTime,
                             String jobHandlerName, String jobHandlerParam, Integer executeIndex) {
        JobLogDO log = JobLogDO.builder().jobId(jobId).handlerName(jobHandlerName)
                .handlerParam(jobHandlerParam).executeIndex(executeIndex)
                .beginTime(beginTime).status(JobLogStatusEnum.RUNNING.getStatus()).build();
        jobLogRepository.insert(log, getLoginUser());
        return log.getId().toHexString();
    }

    @Override
    public String createCompletedJobLog(String jobId, LocalDateTime beginTime, LocalDateTime endTime, String jobHandlerName,
                                        String jobHandlerParam, Integer executeIndex, Integer duration,
                                        boolean success, String result) {
        JobLogDO log = JobLogDO.builder()
                .jobId(jobId)
                .handlerName(jobHandlerName)
                .handlerParam(jobHandlerParam)
                .executeIndex(executeIndex)
                .beginTime(beginTime)
                .endTime(endTime)
                .duration(duration)
                .status(success ? JobLogStatusEnum.SUCCESS.getStatus() : JobLogStatusEnum.FAILURE.getStatus())
                .result(result)
                .build();
        jobLogRepository.insert(log, getLoginUser());
        return log.getId().toHexString();
    }

    @Override
    @Async
    public void updateJobLogResultAsync(String logId, LocalDateTime endTime, Integer duration, boolean success, String result) {
        try {
            JobLogDO updateObj = new JobLogDO();
            updateObj.setId(new ObjectId(logId));
            updateObj.setEndTime(endTime);
            updateObj.setDuration(duration);
            updateObj.setStatus(success ? JobLogStatusEnum.SUCCESS.getStatus() : JobLogStatusEnum.FAILURE.getStatus());
            updateObj.setResult(result);
//            JobLogDO updateObj = JobLogDO.builder().id(new ObjectId(logId)).endTime(endTime).duration(duration)
//                    .status(success ? JobLogStatusEnum.SUCCESS.getStatus() : JobLogStatusEnum.FAILURE.getStatus())
//                    .result(result).build();
            jobLogRepository.updateById(updateObj, getLoginUser());
        } catch (Exception ex) {
            log.warn("[updateJobLogResultAsync][logId({}) endTime({}) duration({}) success({}) result({})]",
                    logId, endTime, duration, success, result);
        }
    }

    @Override
    @SuppressWarnings("DuplicatedCode")
    public Integer cleanJobLog(Integer exceedDay, Integer deleteLimit) {
        int count = 0;
        LocalDateTime expireDate = LocalDateTime.now().minusDays(exceedDay);
        // 循环删除，直到没有满足条件的数据
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            int deleteCount = jobLogRepository.deleteByCreateTimeLt(expireDate, deleteLimit);
            count += deleteCount;
            // 达到删除预期条数，说明到底了
            if (deleteCount < deleteLimit) {
                break;
            }
        }
        return count;
    }

    @Override
    public JobLogDO getJobLog(String id) {
        return jobLogRepository.selectById(id);
    }

    @Override
    public PageResult<JobLogDO> getJobLogPage(JobLogPageReqVO pageReqVO) {
        return jobLogRepository.selectPage(pageReqVO);
    }

}
