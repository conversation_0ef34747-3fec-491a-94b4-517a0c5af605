package ${basePackage}.module.${table.moduleName}.service.${table.businessName};

import org.springframework.stereotype.Service;
import ${jakartaPackage}.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import org.bson.types.ObjectId;
import ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo.*;
import ${basePackage}.module.${table.moduleName}.dal.dataobject.${table.businessName}.${table.className}DO;
## 特殊：主子表专属逻辑
#foreach ($subTable in $subTables)
import ${basePackage}.module.${subTable.moduleName}.dal.dataobject.${subTable.businessName}.${subTable.className}DO; // 主子表专属逻辑
#end
import ${PageResultClassName};
import ${PageParamClassName};
import ${BeanUtils};

import ${basePackage}.module.${table.moduleName}.dal.repository.${table.businessName}.${table.className}Repository;
## 特殊：主子表专属逻辑
#foreach ($subTable in $subTables)
#set ($index = $foreach.count - 1)
import ${basePackage}.module.${subTable.moduleName}.dal.repository.${subTable.businessName}.${subTable.className}Repository; // 主子表专属逻辑
#end

import static ${ServiceExceptionUtilClassName}.exception;
import static ${basePackage}.module.${table.moduleName}.enums.ErrorCodeConstants.*;

/**
 * ${table.classComment} Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ${table.className}ServiceImpl implements ${table.className}Service {

    @Resource
    private ${table.className}Repository ${classNameVar}Repository;
## 特殊：主子表专属逻辑
#foreach ($subTable in $subTables)
#set ($index = $foreach.count - 1)
    @Resource
    private ${subTable.className}Repository ${subClassNameVars.get($index)}Repository; // 主子表专属逻辑
#end

    @Override
## 特殊：主子表专属逻辑（非 ERP 模式）
#if ( $subTables && $subTables.size() > 0 && $table.templateType != 11 )
    @Transactional(rollbackFor = Exception.class) // 主子表专属逻辑
#end
    public String create${simpleClassName}(${sceneEnum.prefixClass}${table.className}SaveReqVO createReqVO) {
## 特殊：树表专属逻辑
#if ( $table.templateType == 2 )
#set ($TreeParentJavaField = $treeParentColumn.javaField.substring(0,1).toUpperCase() + ${treeParentColumn.javaField.substring(1)})##首字母大写
#set ($TreeNameJavaField = $treeNameColumn.javaField.substring(0,1).toUpperCase() + ${treeNameColumn.javaField.substring(1)})##首字母大写
        // 校验${treeParentColumn.columnComment}的有效性(树表专属逻辑)
        validateParent${simpleClassName}(null, createReqVO.get${TreeParentJavaField}());
        // 校验${treeNameColumn.columnComment}的唯一性(树表专属逻辑)
        validate${simpleClassName}${TreeNameJavaField}Unique(null, createReqVO.get${TreeParentJavaField}(), createReqVO.get${TreeNameJavaField}());

#end
        // 插入
        ${table.className}DO ${classNameVar} = BeanUtils.toBean(createReqVO, ${table.className}DO.class);
        ${classNameVar}Repository.insert(${classNameVar}, getLoginUser());
## 特殊：主子表专属逻辑（非 ERP 模式）
#if ( $subTables && $subTables.size() > 0 && $table.templateType != 11 )

        // 插入子表
#foreach ($subTable in $subTables)
#set ($index = $foreach.count - 1)
#set ($subSimpleClassName = $subSimpleClassNames.get($index))
#set ($subJoinColumn = $subJoinColumns.get($index))##当前 join 字段
#set ($SubJoinColumnName = $subJoinColumn.javaField.substring(0,1).toUpperCase() + ${subJoinColumn.javaField.substring(1)})##首字母大写
    #if ( $subTable.subJoinMany)
        create${subSimpleClassName}List(${classNameVar}.getId().toHexString(), createReqVO.get${subSimpleClassNames.get($index)}s()); // 主子表专属逻辑（非 ERP 模式）
    #else
        create${subSimpleClassName}(${classNameVar}.getId().toHexString(), createReqVO.get${subSimpleClassNames.get($index)}()); // 主子表专属逻辑（非 ERP 模式）
    #end
#end
#end
        // 返回
        return ${classNameVar}.getId().toHexString();
    }

    @Override
## 特殊：主子表专属逻辑（非 ERP 模式）
#if ( $subTables && $subTables.size() > 0 && $table.templateType != 11 )
    @Transactional(rollbackFor = Exception.class) // 主子表专属逻辑（非 ERP 模式）
#end
    public void update${simpleClassName}(${sceneEnum.prefixClass}${table.className}SaveReqVO updateReqVO) {
        // 校验存在
        validate${simpleClassName}Exists(updateReqVO.getId());
## 特殊：树表专属逻辑
#if ( $table.templateType == 2 )
#set ($TreeParentJavaField = $treeParentColumn.javaField.substring(0,1).toUpperCase() + ${treeParentColumn.javaField.substring(1)})##首字母大写
#set ($TreeNameJavaField = $treeNameColumn.javaField.substring(0,1).toUpperCase() + ${treeNameColumn.javaField.substring(1)})##首字母大写
        // 校验${treeParentColumn.columnComment}的有效性(树表专属逻辑)
        validateParent${simpleClassName}(updateReqVO.getId().toHexString(), updateReqVO.get${TreeParentJavaField}());
        // 校验${treeNameColumn.columnComment}的唯一性(树表专属逻辑)
        validate${simpleClassName}${TreeNameJavaField}Unique(updateReqVO.getId().toHexString(), updateReqVO.get${TreeParentJavaField}(), updateReqVO.get${TreeNameJavaField}());

#end
        // 更新
        ${table.className}DO updateObj = BeanUtils.toBean(updateReqVO, ${table.className}DO.class);
        ${classNameVar}Repository.updateById(updateObj, getLoginUser());
## 特殊：主子表专属逻辑（非 ERP 模式）
#if ( $subTables && $subTables.size() > 0 && $table.templateType != 11)

        // 更新子表
#foreach ($subTable in $subTables)
#set ($index = $foreach.count - 1)
#set ($subSimpleClassName = $subSimpleClassNames.get($index))
#set ($subJoinColumn = $subJoinColumns.get($index))##当前 join 字段
#set ($SubJoinColumnName = $subJoinColumn.javaField.substring(0,1).toUpperCase() + ${subJoinColumn.javaField.substring(1)})##首字母大写
    #if ( $subTable.subJoinMany)
        update${subSimpleClassName}List(updateReqVO.getId().toHexString(), updateReqVO.get${subSimpleClassNames.get($index)}s()); // 主子表专属逻辑（非 ERP 模式）
    #else
        update${subSimpleClassName}(updateReqVO.getId().toHexString(), updateReqVO.get${subSimpleClassNames.get($index)}()); // 主子表专属逻辑（非 ERP 模式）
    #end
#end
#end
    }

    @Override
## 特殊：主子表专属逻辑
#if ( $subTables && $subTables.size() > 0)
    @Transactional(rollbackFor = Exception.class) // 主子表专属逻辑
#end
    public void delete${simpleClassName}(String id) {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        validate${simpleClassName}Exists(oid);
## 特殊：树表专属逻辑
#if ( $table.templateType == 2 )
#set ($ParentJavaField = $treeParentColumn.javaField.substring(0,1).toUpperCase() + ${treeParentColumn.javaField.substring(1)})##首字母大写
        // 校验是否有子${table.classComment}(树表专属逻辑)
        if (${classNameVar}Repository.selectCountBy${ParentJavaField}(id, getLoginUser()) > 0) {
            throw exception(${simpleClassName_underlineCase.toUpperCase()}_EXITS_CHILDREN);
        }
#end
        // 删除
        ${classNameVar}Repository.deleteById(oid, getLoginUser());
## 特殊：主子表专属逻辑
#if ( $subTables && $subTables.size() > 0)

        // 删除子表
#foreach ($subTable in $subTables)
#set ($index = $foreach.count - 1)
#set ($subSimpleClassName = $subSimpleClassNames.get($index))
#set ($subJoinColumn = $subJoinColumns.get($index))##当前 join 字段
#set ($SubJoinColumnName = $subJoinColumn.javaField.substring(0,1).toUpperCase() + ${subJoinColumn.javaField.substring(1)})##首字母大写
        delete${subSimpleClassName}By${SubJoinColumnName}(id); // 主子表专属逻辑
#end
#end
    }

    private void validate${simpleClassName}Exists(ObjectId id) {
        if (!${classNameVar}Repository.validateExists(id)) {
            throw exception(${simpleClassName_underlineCase.toUpperCase()}_NOT_EXISTS);
        }
    }

## 特殊：树表专属逻辑
#if ( $table.templateType == 2 )
#set ($TreeParentJavaField = $treeParentColumn.javaField.substring(0,1).toUpperCase() + ${treeParentColumn.javaField.substring(1)})##首字母大写
#set ($TreeNameJavaField = $treeNameColumn.javaField.substring(0,1).toUpperCase() + ${treeNameColumn.javaField.substring(1)})##首字母大写
    private void validateParent${simpleClassName}(String id, String ${treeParentColumn.javaField}) { // 树表专属逻辑
        if (${treeParentColumn.javaField} == null || ${simpleClassName}DO.${treeParentColumn_javaField_underlineCase.toUpperCase()}_ROOT.equals(${treeParentColumn.javaField})) {
            return;
        }
        // 1. 不能设置自己为父${table.classComment}
        if (Objects.equals(id, ${treeParentColumn.javaField})) {
            throw exception(${simpleClassName_underlineCase.toUpperCase()}_PARENT_ERROR);
        }
        // 2. 父${table.classComment}不存在
        ${simpleClassName}DO parent${simpleClassName} = ${classNameVar}Repository.selectById(${treeParentColumn.javaField});
        if (parent${simpleClassName} == null) {
            throw exception(${simpleClassName_underlineCase.toUpperCase()}_PARENT_NOT_EXITS);
        }
        // 3. 递归校验父${table.classComment}，如果父${table.classComment}是自己的子${table.classComment}，则报错，避免形成环路
        if (id == null) { // id 为空，说明新增，不需要考虑环路
            return;
        }
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            // 3.1 校验环路
            ${treeParentColumn.javaField} = parent${simpleClassName}.get${TreeParentJavaField}();
            if (Objects.equals(id, ${treeParentColumn.javaField})) {
                throw exception(${simpleClassName_underlineCase.toUpperCase()}_PARENT_IS_CHILD);
            }
            // 3.2 继续递归下一级父${table.classComment}
            if (${treeParentColumn.javaField} == null || ${simpleClassName}DO.${treeParentColumn_javaField_underlineCase.toUpperCase()}_ROOT.equals(${treeParentColumn.javaField})) {
                break;
            }
            parent${simpleClassName} = ${classNameVar}Repository.selectById(${treeParentColumn.javaField});
            if (parent${simpleClassName} == null) {
                break;
            }
        }
    }

    private void validate${simpleClassName}${TreeNameJavaField}Unique(String id, String ${treeParentColumn.javaField}, String ${treeNameColumn.javaField}) { // 树表专属逻辑
        ${simpleClassName}DO ${classNameVar} = ${classNameVar}Repository.selectBy${TreeParentJavaField}And${TreeNameJavaField}(${treeParentColumn.javaField}, ${treeNameColumn.javaField});
        if (${classNameVar} == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的${table.classComment}
        if (id == null) {
            throw exception(${simpleClassName_underlineCase.toUpperCase()}_${treeNameColumn_javaField_underlineCase.toUpperCase()}_DUPLICATE);
        }
        if (!Objects.equals(${classNameVar}.getId(), id)) {
            throw exception(${simpleClassName_underlineCase.toUpperCase()}_${treeNameColumn_javaField_underlineCase.toUpperCase()}_DUPLICATE);
        }
    }

#end
    @Override
    public ${table.className}DO get${simpleClassName}(String id) {
        return ${classNameVar}Repository.selectById(id);
    }

## 特殊：树表专属逻辑（树不需要分页接口）
#if ( $table.templateType != 2 )
    @Override
    public PageResult<${table.className}DO> get${simpleClassName}Page(${sceneEnum.prefixClass}${table.className}PageReqVO pageReqVO) {
        return ${classNameVar}Repository.selectPage(pageReqVO);
    }
#else
    @Override
    public List<${table.className}DO> get${simpleClassName}List(${sceneEnum.prefixClass}${table.className}ListReqVO listReqVO) { // 树表专属逻辑（树不需要分页接口）
        return ${classNameVar}Repository.selectList(listReqVO);
    }
#end

## 特殊：主子表专属逻辑
#foreach ($subTable in $subTables)
#set ($index = $foreach.count - 1)
#set ($subSimpleClassName = $subSimpleClassNames.get($index))
#set ($simpleClassNameUnderlineCase = $simpleClassNameUnderlineCases.get($index))
#set ($subPrimaryColumn = $subPrimaryColumns.get($index))##当前 primary 字段
#set ($subJoinColumn = $subJoinColumns.get($index))##当前 join 字段
#set ($SubJoinColumnName = $subJoinColumn.javaField.substring(0,1).toUpperCase() + ${subJoinColumn.javaField.substring(1)})##首字母大写
#set ($subClassNameVar = $subClassNameVars.get($index))
    // ==================== 子表（$subTable.classComment） ====================

## 情况一：MASTER_ERP 时，需要分查询页子表
#if ( $table.templateType == 11 )
    @Override // 主子表专属逻辑, MASTER_ERP 时，需要分查询页子表
    public PageResult<${subTable.className}DO> get${subSimpleClassName}Page(PageParam pageReqVO, ${subJoinColumn.javaType} ${subJoinColumn.javaField}) {
        return ${subClassNameVars.get($index)}Repository.selectPage(pageReqVO, ${subJoinColumn.javaField});
    }

## 情况二：非 MASTER_ERP 时，需要列表查询子表
#else
    #if ( $subTable.subJoinMany )
    @Override // 主子表专属逻辑, 非 MASTER_ERP 时，需要列表查询子表
    public List<${subTable.className}DO> get${subSimpleClassName}ListBy${SubJoinColumnName}(${subJoinColumn.javaType} ${subJoinColumn.javaField}) {
        return ${subClassNameVars.get($index)}Repository.selectListBy${SubJoinColumnName}(${subJoinColumn.javaField});
    }

    #else
    @Override // 主子表专属逻辑, 非 MASTER_ERP 时，需要列表查询子表
    public ${subTable.className}DO get${subSimpleClassName}By${SubJoinColumnName}(${subJoinColumn.javaType} ${subJoinColumn.javaField}) {
        return ${subClassNameVars.get($index)}Repository.selectBy${SubJoinColumnName}(${subJoinColumn.javaField});
    }

    #end
#end
## 情况一：MASTER_ERP 时，支持单个的新增、修改、删除操作
#if ( $table.templateType == 11 )
    @Override // 主子表专属逻辑, MASTER_ERP 时，支持单个的新增、修改、删除操作
    public String create${subSimpleClassName}(${subTable.className}DO ${subClassNameVar}) {
## 特殊：一对一时，需要保证只有一条，不能重复插入
#if ( !$subTable.subJoinMany)
        // 校验是否已经存在, 主子表专属逻辑, 一对一时，需要保证只有一条，不能重复插入
        if (${subClassNameVars.get($index)}Repository.selectBy${SubJoinColumnName}(${subClassNameVar}.get${SubJoinColumnName}()) != null) {
            throw exception(${simpleClassNameUnderlineCase.toUpperCase()}_EXISTS);
        }
        // 插入
#end
        ${subClassNameVars.get($index)}Repository.insert(${subClassNameVar}, getLoginUser());
        return ${subClassNameVar}.getId().toHexString();
    }

    @Override
    public void update${subSimpleClassName}(${subTable.className}DO ${subClassNameVar}) {
        // 校验存在
        validate${subSimpleClassName}Exists(${subClassNameVar}.getId());
        // 更新
        ${subClassNameVars.get($index)}Repository.updateById(${subClassNameVar}, getLoginUser());
    }

    @Override
    public void delete${subSimpleClassName}(String id) {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        validate${subSimpleClassName}Exists(oid);
        // 删除
        ${subClassNameVars.get($index)}Repository.deleteById(oid, getLoginUser());
    }

    @Override
    public ${subTable.className}DO get${subSimpleClassName}(String id) {
        return ${subClassNameVars.get($index)}Repository.selectById(id);
    }

    private void validate${subSimpleClassName}Exists(${subPrimaryColumn.javaType} id) {
        if (!${subClassNameVar}Repository.validateExists(id)) {
            throw exception(${simpleClassNameUnderlineCase.toUpperCase()}_NOT_EXISTS);
        }
    }

## 情况二：非 MASTER_ERP 时，支持批量的新增、修改操作
#else
    #if ( $subTable.subJoinMany)
    private void create${subSimpleClassName}List(String ${subJoinColumn.javaField}, List<${subTable.className}DO> list) {
        list.forEach(o -> o.set$SubJoinColumnName(${subJoinColumn.javaField})); // 非 MASTER_ERP 时，支持批量的新增、修改操作
        ${subClassNameVars.get($index)}Repository.insertBatch(list, getLoginUser());
    }

    private void update${subSimpleClassName}List(String ${subJoinColumn.javaField}, List<${subTable.className}DO> list) {
    ##    delete${subSimpleClassName}By${SubJoinColumnName}(${subJoinColumn.javaField});
	##	list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null));
    ##    create${subSimpleClassName}List(${subJoinColumn.javaField}, list);
        // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        list.forEach(o ->
                ${subClassNameVars.get($index)}Repository.upsert${subSimpleClassName}(${subJoinColumn.javaField}, o, getLoginUser())
        );
    }

    #else
    private void create${subSimpleClassName}(String ${subJoinColumn.javaField}, ${subTable.className}DO ${subClassNameVar}) {
        if (${subClassNameVar} == null) {
            return;
        }
        ${subClassNameVar}.set$SubJoinColumnName(${subJoinColumn.javaField});
        ${subClassNameVars.get($index)}Repository.insert(${subClassNameVar}, getLoginUser());
    }

    private void update${subSimpleClassName}(String ${subJoinColumn.javaField}, ${subTable.className}DO ${subClassNameVar}) {
        if (${subClassNameVar} == null) {
			return;
        }
        ${subClassNameVar}.set$SubJoinColumnName(${subJoinColumn.javaField});
    ##    ${subClassNameVar}.setUpdater(null).setUpdateTime(null);
        ${subClassNameVars.get($index)}Repository.insertOrUpdate(${subClassNameVar}, getLoginUser()); // 解决更新情况下：updateTime 不更新
    }

    #end
#end
    private void delete${subSimpleClassName}By${SubJoinColumnName}(String ${subJoinColumn.javaField}) {
        ${subClassNameVars.get($index)}Repository.deleteBy${SubJoinColumnName}(${subJoinColumn.javaField}, getLoginUser());
    }

#end
}