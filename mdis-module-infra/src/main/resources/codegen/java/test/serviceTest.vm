package ${basePackage}.module.${table.moduleName}.service.${table.businessName};

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.bson.types.ObjectId;

import ${jakartaPackage}.annotation.Resource;

import ${baseFrameworkPackage}.test.core.ut.BaseDbUnitTest;
import ${baseFrameworkPackage}.mongo.core.query.LambdaQueryWrapperMongo;
import ${baseFrameworkPackage}.tenant.config.TenantProperties;
import ${baseFrameworkPackage}.tenant.core.db.TenantMongoDBHandler;
import ${baseFrameworkPackage}.tenant.core.util.TenantMongoUtils;
import ${baseFrameworkPackage}.security.core.LoginUser;
import ${baseFrameworkPackage}.tenant.core.context.TenantContextHolder;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.Authentication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.context.annotation.ComponentScan;
import com.mongodb.client.result.UpdateResult;

import ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo.*;
import ${basePackage}.module.${table.moduleName}.dal.dataobject.${table.businessName}.${table.className}DO;
import ${basePackage}.module.${table.moduleName}.dal.repository.${table.businessName}.${table.className}Repository;
import ${PageResultClassName};

import ${jakartaPackage}.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.RoundingMode;

import static ${baseFrameworkPackage}.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static cn.hutool.core.util.RandomUtil.*;
import static ${basePackage}.module.${table.moduleName}.enums.ErrorCodeConstants.*;
import static ${baseFrameworkPackage}.test.core.util.AssertUtils.*;
import static ${baseFrameworkPackage}.test.core.util.RandomUtils.*;
import static ${baseFrameworkPackage}.common.util.date.LocalDateTimeUtils.*;
import static ${baseFrameworkPackage}.common.util.object.ObjectUtils.*;
import static ${baseFrameworkPackage}.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

## 字段模板
#macro(getPageCondition $VO)
       // mock 数据
       ${table.className}DO db${simpleClassName} = randomPojo(${table.className}DO.class, o -> { // 等会查询到
       #foreach ($column in $columns)
       #if (${column.listOperation})
       #set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
           o.set$JavaField("test-${column.javaField}");
       #end
       #end
       });
       ${classNameVar}Repository.insert(db${simpleClassName}, getLoginUser());
       // 准备参数
       ${sceneEnum.prefixClass}${table.className}${VO} reqVO = new ${sceneEnum.prefixClass}${table.className}${VO}();
       #foreach ($column in $columns)
       #if (${column.listOperation})
       #set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
       #if (${column.listOperationCondition} == "BETWEEN")## BETWEEN 的情况
       reqVO.set${JavaField}(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       #else
       reqVO.set$JavaField(null);
       #end
       #end
       #end
#end
/**
 * {@link ${table.className}ServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(${table.className}ServiceImpl.class)
@ExtendWith(MockitoExtension.class)
public class ${table.className}ServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ${table.className}ServiceImpl ${classNameVar}Service;

    @Mock
    private ${table.className}Repository ${classNameVar}Repository;

    @Mock
    private TenantProperties tenantProperties;
    
    // 用于模拟存储的列表
    private List<${table.className}DO> mock${simpleClassName}s = new ArrayList<>();

    @Mock
    private /* 这里填写实际的API类型 */ externalApi;

    @BeforeEach
    void setUp() {
        // 设置模拟登录用户和租户上下文
        setupMockUser();
        
        // 获取集合名并配置到租户属性中
        String collectionName = ${table.className}DO.class.getAnnotation(Document.class).collection();
        when(tenantProperties.getIgnoreTables()).thenReturn(Collections.singleton(collectionName));
        
        // 清空模拟数据库
        mock${simpleClassName}s.clear();
        
        // 配置repository模拟行为
        setupMockRepository();
    }
    
    private void setupMockRepository() {
        // 模拟insert方法
        when(${classNameVar}Repository.insert(any(${table.className}DO.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ${table.className}DO entity = invocation.getArgument(0);
            if (entity.getId() == null) {
                entity.setId(new ObjectId());
            }
            mock${simpleClassName}s.add(entity);
            return entity;
        });
        
        // 模拟selectById方法 - 修复String和ObjectId比较逻辑
        when(${classNameVar}Repository.selectById(any(String.class))).thenAnswer(invocation -> {
            String id = invocation.getArgument(0);
            return mock${simpleClassName}s.stream()
                .filter(r -> r.getId().toHexString().equals(id))
                .findFirst()
                .orElse(null);
        });
        
        // 模拟validateExists方法
        when(${classNameVar}Repository.validateExists(any(ObjectId.class))).thenAnswer(invocation -> {
            ObjectId id = invocation.getArgument(0);
            return mock${simpleClassName}s.stream()
                .anyMatch(r -> r.getId().equals(id));
        });
        
        // 模拟update方法 - 返回UpdateResult
        when(${classNameVar}Repository.updateById(any(${table.className}DO.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ${table.className}DO newEntity = invocation.getArgument(0);
            boolean updated = false;
            for (int i = 0; i < mock${simpleClassName}s.size(); i++) {
                if (mock${simpleClassName}s.get(i).getId().equals(newEntity.getId())) {
                    mock${simpleClassName}s.set(i, newEntity);
                    updated = true;
                    break;
                }
            }
            UpdateResult result = mock(UpdateResult.class);
            when(result.getMatchedCount()).thenReturn(updated ? 1L : 0L);
            when(result.getModifiedCount()).thenReturn(updated ? 1L : 0L);
            return result;
        });
        
        // 模拟deleteById方法 - 返回UpdateResult
        when(${classNameVar}Repository.deleteById(any(ObjectId.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ObjectId id = invocation.getArgument(0);
            boolean removed = mock${simpleClassName}s.removeIf(r -> r.getId().equals(id));
            UpdateResult result = mock(UpdateResult.class);
            when(result.getMatchedCount()).thenReturn(removed ? 1L : 0L);
            when(result.getModifiedCount()).thenReturn(removed ? 1L : 0L);
            return result;
        });
        
        // 模拟分页查询
        when(${classNameVar}Repository.selectPage(any(${sceneEnum.prefixClass}${table.className}PageReqVO.class))).thenAnswer(invocation -> {
            // 简单返回所有数据，实际项目中应该根据查询条件过滤
            return new PageResult<${table.className}DO>(mock${simpleClassName}s, (long) mock${simpleClassName}s.size());
        });
        
        // 模拟deleteByQuery方法
        when(${classNameVar}Repository.deleteByQuery(any())).thenAnswer(invocation -> {
            int size = mock${simpleClassName}s.size();
            mock${simpleClassName}s.clear();
            return size;
        });
    }

    @AfterEach
    public void tearDown() {
        // 清理安全上下文和租户上下文
        SecurityContextHolder.clearContext();
        TenantContextHolder.clear();
        // 清空模拟数据
        mock${simpleClassName}s.clear();
    }

    // 在每个测试方法前设置模拟登录用户
    private void setupMockUser() {
        // 设置模拟登录用户
        LoginUser loginUser = new LoginUser();
        loginUser.setId("mock-user-id");
        Authentication authentication = new UsernamePasswordAuthenticationToken(
                loginUser, null, Collections.emptyList());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        // 设置具体的租户ID
        TenantContextHolder.setTenantId("mock-tenant-id");
        // 设置忽略租户，以避免MongoDB租户相关问题
        TenantContextHolder.setIgnore(true);
    }

    @Test
    public void testCreate${simpleClassName}_success() {
        // 准备参数
        ${sceneEnum.prefixClass}${table.className}SaveReqVO createReqVO = randomPojo(${sceneEnum.prefixClass}${table.className}SaveReqVO.class, o -> {
            o.setId(null);
#foreach ($column in $columns)
#if (${column.javaType} == "BigDecimal")
#set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
            o.set$JavaField(new BigDecimal("123.45").setScale(2, RoundingMode.HALF_UP));
#end
#end
        });

        // 调用
        String ${classNameVar}Id = ${classNameVar}Service.create${simpleClassName}(createReqVO);
        
        // 断言
        assertNotNull(${classNameVar}Id);
        // 校验记录的属性是否正确
        ${table.className}DO ${classNameVar} = ${classNameVar}Repository.selectById(${classNameVar}Id);
        assertNotNull(${classNameVar});
        assertPojoEquals(createReqVO, ${classNameVar}, "id");
    }

    @Test
    public void testUpdate${simpleClassName}_success() {
        // mock 数据
        ${table.className}DO db${simpleClassName} = randomPojo(${table.className}DO.class, o -> {
#foreach ($column in $columns)
#if (${column.javaType} == "BigDecimal")
#set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
            o.set$JavaField(new BigDecimal("1234567890.12345678").setScale(8, RoundingMode.HALF_UP));
#end
#end
        });
        ${classNameVar}Repository.insert(db${simpleClassName}, getLoginUser());
        
        // 准备参数
        ${sceneEnum.prefixClass}${table.className}SaveReqVO updateReqVO = randomPojo(${sceneEnum.prefixClass}${table.className}SaveReqVO.class, o -> {
            o.setId(db${simpleClassName}.getId()); // 设置更新的 ID
#foreach ($column in $columns)
#if (${column.javaType} == "BigDecimal")
#set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
            o.set$JavaField(new BigDecimal("9876543210.98765432").setScale(8, RoundingMode.HALF_UP));
#end
#end
        });

        // 调用
        ${classNameVar}Service.update${simpleClassName}(updateReqVO);
        
        // 校验是否更新正确
        ${table.className}DO ${classNameVar} = ${classNameVar}Repository.selectById(updateReqVO.getId().toHexString());
        assertNotNull(${classNameVar});
        assertPojoEquals(updateReqVO, ${classNameVar});
    }

    @Test
    public void testUpdate${simpleClassName}_notExists() {
        // 准备参数
        ${sceneEnum.prefixClass}${table.className}SaveReqVO updateReqVO = randomPojo(${sceneEnum.prefixClass}${table.className}SaveReqVO.class, o -> {
            o.setId(new ObjectId());
        });

        // 调用, 并断言异常
        assertServiceException(() -> ${classNameVar}Service.update${simpleClassName}(updateReqVO), ${simpleClassName_underlineCase.toUpperCase()}_NOT_EXISTS);
    }

    @Test
    public void testDelete${simpleClassName}_success() {
        // mock 数据
        ${table.className}DO db${simpleClassName} = randomPojo(${table.className}DO.class, o -> {
#foreach ($column in $columns)
#if (${column.javaType} == "BigDecimal")
#set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
            o.set$JavaField(new BigDecimal("123.45").setScale(2, RoundingMode.HALF_UP));
#end
#end
        });
        ${classNameVar}Repository.insert(db${simpleClassName}, getLoginUser());
        
        // 准备参数
        String id = db${simpleClassName}.getId().toHexString();

        // 调用
        ${classNameVar}Service.delete${simpleClassName}(id);
        
        // 校验数据不存在了
        assertNull(${classNameVar}Repository.selectById(id));
    }

    @Test
    public void testDelete${simpleClassName}_notExists() {
        // 准备参数
        ObjectId id = new ObjectId();
        
        // 调用, 并断言异常
        assertServiceException(() -> ${classNameVar}Service.delete${simpleClassName}(id.toHexString()), ${simpleClassName_underlineCase.toUpperCase()}_NOT_EXISTS);
    }

## 特殊：树表专属逻辑（树不需要分页接口）
#if ( $table.templateType != 2 )
    @Test
    public void testGet${simpleClassName}Page() {
        // 简化测试，只创建一条记录
        ${table.className}DO db${simpleClassName} = randomPojo(${table.className}DO.class, o -> {
#foreach ($column in $columns)
#if (${column.javaType} == "BigDecimal")
#set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
            o.set$JavaField(new BigDecimal("123.45").setScale(2, RoundingMode.HALF_UP));
#end
#end
            
            // 清除可能影响查询的字段
#foreach ($column in $columns)
#if (${column.listOperation})
#set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
            o.set$JavaField("test-${column.javaField}");
#end
#end
        });
        ${classNameVar}Repository.insert(db${simpleClassName}, getLoginUser());
        
        // 准备参数 - 使用最简单的空查询条件
        ${sceneEnum.prefixClass}${table.className}PageReqVO reqVO = new ${sceneEnum.prefixClass}${table.className}PageReqVO();
        
        // 调用
        PageResult<${table.className}DO> pageResult = ${classNameVar}Service.get${simpleClassName}Page(reqVO);
        
        // 断言 - 只检查是否能找到我们创建的记录
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(db${simpleClassName}, pageResult.getList().get(0));
    }
#else
    @Test
    public void testGet${simpleClassName}List() {
        // 简化测试，只创建一条记录
        ${table.className}DO db${simpleClassName} = randomPojo(${table.className}DO.class, o -> {
#foreach ($column in $columns)
#if (${column.javaType} == "BigDecimal")
#set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
            o.set$JavaField(new BigDecimal("123.45").setScale(2, RoundingMode.HALF_UP));
#end
#end
            
            // 清除可能影响查询的字段
#foreach ($column in $columns)
#if (${column.listOperation})
#set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
            o.set$JavaField("test-${column.javaField}");
#end
#end
        });
        ${classNameVar}Repository.insert(db${simpleClassName}, getLoginUser());
        
        // 准备参数 - 使用最简单的空查询条件
        ${sceneEnum.prefixClass}${table.className}ListReqVO reqVO = new ${sceneEnum.prefixClass}${table.className}ListReqVO();
        
        // 调用
        List<${table.className}DO> list = ${classNameVar}Service.get${simpleClassName}List(reqVO);
        
        // 断言 - 只检查是否能找到我们创建的记录
        assertEquals(1, list.size());
        assertPojoEquals(db${simpleClassName}, list.get(0));
    }
#end

}