#set ($subTable = $subTables.get($subIndex))##当前表
#set ($subColumns = $subColumnsList.get($subIndex))##当前字段数组
package ${basePackage}.module.${subTable.moduleName}.dal.dataobject.${subTable.businessName};

import lombok.*;
import java.util.*;
import ${BaseDOClassName};
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
#foreach ($column in $columns)
#if (${column.javaType} == "BigDecimal")
import java.math.BigDecimal;
#break
#end
#end
#foreach ($column in $columns)
#if (${column.javaType} == "LocalDateTime")
import java.time.LocalDateTime;
#break
#end
#end

/**
 * ${subTable.classComment} DO
 *
 * <AUTHOR>
 */
@Document("${subTable.tableName.toLowerCase()}")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ${subTable.className}DO extends BaseEntity {

#foreach ($column in $subColumns)
#if (!${baseDOFields.contains(${column.javaField})})##排除 BaseDO 的字段
    /**
     * ${column.columnComment}
    #if ("$!column.dictType" != "")##处理枚举值
     *
     * 枚举 {@link TODO ${column.dictType} 对应的类}
    #end
     */
    #if (${column.columnName} != ${column.javaField})## 处理column名称和 field 名称不同的情况
    @Field("${column.columnName}")
    #end
    private ${column.javaType} ${column.javaField};
#end
#end

}