#set ($subTable = $subTables.get($subIndex))##当前表
#set ($subColumns = $subJoinColumnsList.get($subIndex))##当前字段数组
#set ($subJoinColumn = $subJoinColumns.get($subIndex))##当前 join 字段
#set ($SubJoinColumnName = $subJoinColumn.javaField.substring(0,1).toUpperCase() + ${subJoinColumn.javaField.substring(1)})##首字母大写
package ${basePackage}.module.${subTable.moduleName}.dal.repository.${subTable.businessName};

import java.util.*;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import com.mongodb.client.result.UpdateResult;

import ${PageResultClassName};
import ${PageParamClassName};
import ${QueryWrapperClassName};
import ${basePackage}.module.${subTable.moduleName}.dal.dataobject.${subTable.businessName}.${subTable.className}DO;

/**
 * ${subTable.classComment} Repository
 *
 * <AUTHOR>
 */
@Repository
public class ${subTable.className}Repository extends BaseRepository<${table.className}DO, ObjectId> {
    public ${table.className}Repository(MongoTemplate mongoOperations) {
        super(${table.className}DO.class, mongoOperations);
    }

## 情况一：MASTER_ERP 时，需要分查询页子表
#if ( $table.templateType == 11 )
    public PageResult<${subTable.className}DO> selectPage(PageParam reqVO, ${subJoinColumn.javaType} ${subJoinColumn.javaField}) {
        LambdaQueryWrapperMongo<${subTable.className}DO> queryWrapper = new LambdaQueryWrapperMongo<>(${subTable.className}DO.class)
                .eq(${subTable.className}DO::get${SubJoinColumnName}, ${subJoinColumn.javaField})
                .orderByDesc(${subTable.className}DO::getId);## 大多数情况下，id 倒序
        return selectPage(reqVO, queryWrapper);

    }
## 主表与子表是一对一时 
    #if (!$subTable.subJoinMany)
    public ${subTable.className}DO selectBy${SubJoinColumnName}(${subJoinColumn.javaType} ${subJoinColumn.javaField}) {
        LambdaQueryWrapperMongo<${subTable.className}DO> queryWrapper = new LambdaQueryWrapperMongo<>(${subTable.className}DO.class)
            .eq(${subTable.className}DO::get${SubJoinColumnName}, ${subJoinColumn.javaField});
        return findOne(queryWrapper).orElse(null);
    }
    #end

## 情况二：非 MASTER_ERP 时，需要列表查询子表
#else
    #if ( $subTable.subJoinMany)
    public List<${subTable.className}DO> selectListBy${SubJoinColumnName}(${subJoinColumn.javaType} ${subJoinColumn.javaField}) {
        LambdaQueryWrapperMongo<${subTable.className}DO> queryWrapper = new LambdaQueryWrapperMongo<>(${subTable.className}DO.class)
                .eq(${subTable.className}DO::get${SubJoinColumnName}, ${subJoinColumn.javaField});
        return selectList(queryWrapper);
    }

    #else
    public ${subTable.className}DO selectBy${SubJoinColumnName}(${subJoinColumn.javaType} ${subJoinColumn.javaField}) {
        LambdaQueryWrapperMongo<${subTable.className}DO> queryWrapper = new LambdaQueryWrapperMongo<>(${subTable.className}DO.class)
                .eq(${subTable.className}DO::get${SubJoinColumnName}, ${subJoinColumn.javaField});
        return findOne(queryWrapper).orElse(null);
    }

    #end
    #end
    public int deleteBy${SubJoinColumnName}(${subJoinColumn.javaType} ${subJoinColumn.javaField}, LoginUser loginUser) {
        LambdaQueryWrapperMongo<${subTable.className}DO> queryWrapper = new LambdaQueryWrapperMongo<>(${subTable.className}DO.class)
                .eq(${subTable.className}DO::get${SubJoinColumnName}, ${subJoinColumn.javaField});
        return softDeleteByQuery(queryWrapper, loginUser);
    }

}
