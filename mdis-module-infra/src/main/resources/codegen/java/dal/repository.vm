package ${basePackage}.module.${table.moduleName}.dal.repository.${table.businessName};

import java.util.*;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import com.mongodb.client.result.UpdateResult;

import ${PageResultClassName};
import ${QueryWrapperClassName};
import ${basePackage}.module.${table.moduleName}.dal.dataobject.${table.businessName}.${table.className}DO;
import ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo.*;

## 字段模板
#macro(listCondition)
#foreach ($column in $columns)
#if (${column.listOperation})
#set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
#if (${column.listOperationCondition} == "=")##情况一，= 的时候
                .eqIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "!=")##情况二，!= 的时候
                .neIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == ">")##情况三，> 的时候
                .gtIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == ">=")##情况四，>= 的时候
                .geIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "<")##情况五，< 的时候
                .ltIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "<=")##情况五，<= 的时候
                .leIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "LIKE")##情况七，Like 的时候
                .likeIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "BETWEEN")##情况八，Between 的时候
                .betweenIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#end
#end
#end
/**
 * ${table.classComment} Repository
 *
 * <AUTHOR>
 */
@Repository
public class ${table.className}Repository extends BaseRepository<${table.className}DO, ObjectId> {
    public ${table.className}Repository(MongoTemplate mongoOperations) {
        super(${table.className}DO.class, mongoOperations);
    }

## 特殊：树表专属逻辑（树不需要分页接口）
#if ( $table.templateType != 2 )
    public PageResult<${table.className}DO> selectPage(${sceneEnum.prefixClass}${table.className}PageReqVO reqVO) {
        LambdaQueryWrapperMongo<${table.className}DO> queryWrapper = new LambdaQueryWrapperMongo<>(${table.className}DO.class)
            #listCondition()
                .orderByDesc(${table.className}DO::getId);## 大多数情况下，id 倒序

        return selectPage(reqVO, queryWrapper);
    }
#else
    public List<${table.className}DO> selectList(${sceneEnum.prefixClass}${table.className}ListReqVO reqVO) {
        LambdaQueryWrapperMongo<${table.className}DO> queryWrapper = new LambdaQueryWrapperMongo<>(${table.className}DO.class)
            #listCondition()
                .orderByDesc(${table.className}DO::getId);## 大多数情况下，id 倒序
        return selectList(queryWrapper);
    }
#end

## 特殊：树表专属逻辑
#if ( $table.templateType == 2 )
#set ($TreeParentJavaField = $treeParentColumn.javaField.substring(0,1).toUpperCase() + ${treeParentColumn.javaField.substring(1)})##首字母大写
#set ($TreeNameJavaField = $treeNameColumn.javaField.substring(0,1).toUpperCase() + ${treeNameColumn.javaField.substring(1)})##首字母大写
	public ${table.className}DO selectBy${TreeParentJavaField}And${TreeNameJavaField}(String ${treeParentColumn.javaField}, String ${treeNameColumn.javaField}) {
        LambdaQueryWrapperMongo<${table.className}DO> queryWrapper = new LambdaQueryWrapperMongo<>(${table.className}DO.class)
                .eq(${table.className}DO::get${TreeParentJavaField}, ${treeParentColumn.javaField})
                .eq(${table.className}DO::get${TreeNameJavaField}, ${treeNameColumn.javaField});
	    return findOne(queryWrapper).orElse(null);
	}

    public Long selectCountBy${TreeParentJavaField}(${treeParentColumn.javaType} ${treeParentColumn.javaField}) {
        LambdaQueryWrapperMongo<${table.className}DO> queryWrapper = new LambdaQueryWrapperMongo<>(${table.className}DO.class)
                .eq(${table.className}DO::get${TreeParentJavaField}, ${treeParentColumn.javaField});
        return count(queryWrapper);
    }
#end

## 通用
    public ${table.className}DO selectById(String id) {
        LambdaQueryWrapperMongo<${table.className}DO> queryWrapper = new LambdaQueryWrapperMongo<>(${table.className}DO.class)
                .eq(${table.className}DO::getId, id);
        return findOne(queryWrapper).orElse(null);
    }

    public boolean validateExists(ObjectId id) {
        LambdaQueryWrapperMongo<${table.className}DO> queryWrapper = new LambdaQueryWrapperMongo<>(${table.className}DO.class)
                .eq(${table.className}DO::getId, id);
        return existsByQuery(queryWrapper);
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        LambdaQueryWrapperMongo<${table.className}DO> queryWrapper = new LambdaQueryWrapperMongo<>(${table.className}DO.class)
                .eq(${table.className}DO::getId, id);
        return softDeleteByQuery(queryWrapper, loginUser);
    }

}