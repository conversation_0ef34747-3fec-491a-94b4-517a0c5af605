package ${basePackage}.module.${table.moduleName}.dal.dataobject.${table.businessName};

import lombok.*;
import java.util.*;
import ${BaseDOClassName};
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
#foreach ($column in $columns)
#if (${column.javaType} == "BigDecimal")
import java.math.BigDecimal;
#break
#end
#end
#foreach ($column in $columns)
#if (${column.javaType} == "LocalDateTime")
import java.time.LocalDateTime;
#break
#end
#end

/**
 * ${table.classComment} DO
 *
 * <AUTHOR>
 */
@Document("${table.tableName.toLowerCase()}")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ${table.className}DO extends BaseEntity {

## 特殊：树表专属逻辑
#if ( $table.templateType == 2 )
    public static final String ${treeParentColumn_javaField_underlineCase.toUpperCase()}_ROOT = "0";

#end
#foreach ($column in $columns)
#if (!${baseDOFields.contains(${column.javaField})})##排除 BaseDO 的字段
    /**
     * ${column.columnComment}
    #if ("$!column.dictType" != "")##处理枚举值
     *
     * 枚举 {@link TODO ${column.dictType} 对应的类}
    #end
     */
    #if (${column.columnName} != ${column.javaField})## 处理column名称和 field 名称不同的情况
    @Field("${column.columnName}")
    #end
    private ${column.javaType} ${column.javaField};
#end
#end

}