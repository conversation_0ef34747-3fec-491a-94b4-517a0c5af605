// MongoDB 菜单插入脚本
const currentDate = new Date();

// 插入主菜单
db.system_menu.insertOne({
  visible: true,
  create_time: currentDate,
  component_name: "${table.className}",
  icon: "",
  always_show: true,
  permission: "",
  sort: 0,
  type: 2,
  path: "${simpleClassName_strikeCase}",
  component: "${table.moduleName}/${table.businessName}/index",
  update_time: currentDate,
  deleted: false,
  parent_id: "${table.parentMenuId}",
  name: "${table.classComment}",
  keep_alive: true,
  status: 0
});

// 获取插入的主菜单ID
const parentMenu = db.system_menu.findOne({
  name: "${table.classComment}",
  parent_id: "${table.parentMenuId}"
});
const parentId = parentMenu._id.toString();

// 插入按钮菜单
#set ($functionNames = ['查询', '创建', '更新', '删除', '导出'])
#set ($functionOps = ['query', 'create', 'update', 'delete', 'export'])
#foreach ($functionName in $functionNames)
#set ($index = $foreach.count - 1)
db.system_menu.insertOne({
  visible: true,
  create_time: currentDate,
  component_name: "",
  icon: "",
  always_show: true,
  permission: "${permissionPrefix}:${functionOps.get($index)}",
  sort: $foreach.count,
  type: 3,
  path: "",
  component: "",
  update_time: currentDate,
  deleted: false,
  parent_id: parentId,
  name: "${table.classComment}${functionName}",
  keep_alive: true,
  status: 0
});
#end