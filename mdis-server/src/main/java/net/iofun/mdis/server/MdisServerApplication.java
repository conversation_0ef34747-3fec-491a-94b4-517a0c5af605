package net.iofun.mdis.server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
/**
 * 项目的启动类
 *
 * <AUTHOR>
 */
@SuppressWarnings("SpringComponentScan") // 忽略 IDEA 无法识别 ${mdis.info.base-package}
@SpringBootApplication(scanBasePackages = {"${mdis.info.base-package}.server", "${mdis.info.base-package}.module", "${mdis.info.base-package}.framework.mongo"})
public class MdisServerApplication {

    public static void main(String[] args) {
        // 启动 Mock Redis 服务, 需要在POM 中添加 jedis-mock 依赖, 需要跟 yaml 配置文件中的端口一致
        // RedisServer redisServer = new RedisServer(6379);
        // try {
        //     redisServer.start();
        // } catch (IOException e) {
        //     throw new RuntimeException(e);
        // }

        SpringApplication.run(MdisServerApplication.class, args);
    }
}
