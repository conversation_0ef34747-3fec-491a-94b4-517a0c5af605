<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>net.iofun</groupId>
        <artifactId>mdis</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mdis-server</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        后端 Server 的主项目，通过引入需要 mdis-module-xxx 的依赖，
        从而实现提供 RESTful API 给 mdis-ui-admin、mdis-ui-user 等前端项目。
        本质上来说，它就是个空壳（容器）！
    </description>

    <dependencies>
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-module-system</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-module-infra</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-module-product</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 劳务成本管理 -->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-labor-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <!-- 材料采购管理 -->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-material-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <!-- 成本基础管理 -->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-cost-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <!-- 价格行情管理 -->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-trends-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        
        <!-- 
        项目大量使用了 Redis 作为缓存、分布式锁、幂等性等功能，
        直接去除 Redis 会导致项目无法正常运行, 
        所以启动 Mock Redis 模拟服务,不真实依赖 Redis 运行;
        需要配套在
        -->
        <!-- <dependency>
            <groupId>com.github.fppt</groupId>
            <artifactId>jedis-mock</artifactId>
            <version>1.1.2</version>
        </dependency> -->


        <!-- 会员中心。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-member-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- 数据报表。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-report-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <!-- 工作流。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-bpm-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <!-- 支付服务。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-pay-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- 微信公众号模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-mp-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- 商城相关模块。默认注释，保证编译速度-->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-promotion-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-product-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-trade-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-statistics-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- CRM 相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-crm-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- ERP 相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-module-erp-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- AI 大模型相关模块。默认注释，保证编译速度 -->
<!--       <dependency>-->
<!--           <groupId>net.iofun</groupId>-->
<!--           <artifactId>mdis-module-ai</artifactId>-->
<!--           <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- META-INF boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-protection</artifactId>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
