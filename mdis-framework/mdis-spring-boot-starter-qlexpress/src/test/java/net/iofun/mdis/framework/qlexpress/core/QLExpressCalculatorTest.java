package net.iofun.mdis.framework.qlexpress.core;

import net.iofun.mdis.framework.qlexpress.config.QLExpressProperties;
import net.iofun.mdis.framework.common.vo.computation.ComputationResult;
import net.iofun.mdis.framework.common.vo.computation.ExpressionRequest;
import net.iofun.mdis.framework.common.vo.computation.ExpressionVariable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * QLExpress 表达式计算器测试
 *
 * <AUTHOR>
 */
class QLExpressCalculatorTest {

    private QLExpressCalculator calculator;
    
    @BeforeEach
    void setUp() throws Exception {
        QLExpressProperties properties = new QLExpressProperties();
        properties.setPreciseEnabled(true);
        properties.setCacheEnabled(true);
        properties.setTimeoutMs(1000);

        calculator = new QLExpressCalculator(properties);
        calculator.afterPropertiesSet();
    }
    
    @Test
    void testSimpleCalculation() {
        ExpressionRequest request = ExpressionRequest.builder()
                .expression("1 + 2 * 3")
                .build();
        
        ComputationResult result = calculator.calculate(request);
        
        assertTrue(result.getSuccess());
        assertEquals(new BigDecimal("7"), result.getResult());
    }
    
    @Test
    void testVariableCalculation() {
        ExpressionVariable var1 = ExpressionVariable.builder()
                .name("a")
                .value(new BigDecimal("10"))
                .build();
        
        ExpressionVariable var2 = ExpressionVariable.builder()
                .name("b")
                .value(new BigDecimal("5"))
                .build();
        
        ExpressionRequest request = ExpressionRequest.builder()
                .expression("a + b * 2")
                .variables(Arrays.asList(var1, var2))
                .build();
        
        ComputationResult result = calculator.calculate(request);
        
        assertTrue(result.getSuccess());
        assertEquals(new BigDecimal("20"), result.getResult());
    }
    
    @Test
    void testPiFunction() {
        ExpressionRequest request = ExpressionRequest.builder()
                .expression("PI()")
                .build();
        ComputationResult result = calculator.calculate(request);
        System.out.println("PI() result: " + result.getResult());

        assertTrue(result.getSuccess());
        assertEquals(Math.PI, result.getResult().doubleValue(), 0.0001);
    }
    
    @Test
    void testPiConstant() {
        ExpressionRequest request = ExpressionRequest.builder()
                .expression("POW(9,2)")
                .build();

        ComputationResult result = calculator.calculate(request);
        System.out.println("POW(9,2) result: " + result.getResult());

        assertTrue(result.getSuccess());
        assertEquals(Math.pow(9, 2), result.getResult().doubleValue(), 0.0001);
    }
    
    @Test
    void testValidateExpression() {
        assertTrue(calculator.validateExpression("1 + 2"));
        assertTrue(calculator.validateExpression("a * b + c"));
        assertFalse(calculator.validateExpression(""));
        assertFalse(calculator.validateExpression(null));
    }
}
