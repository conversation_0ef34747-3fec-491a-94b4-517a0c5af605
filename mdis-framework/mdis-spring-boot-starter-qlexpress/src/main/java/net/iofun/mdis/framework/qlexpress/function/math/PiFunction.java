package net.iofun.mdis.framework.qlexpress.function.math;

import com.alibaba.qlexpress4.runtime.QContext;
import com.alibaba.qlexpress4.runtime.Parameters;
import com.alibaba.qlexpress4.runtime.function.CustomFunction;

/**
 * Pi 函数，获取 Math.PI 的值
 * 参考文档：https://github.com/alibaba/QLExpress?tab=readme-ov-file#添加自定义函数与操作符
 *
 * <AUTHOR>
 */
public class PiFunction implements CustomFunction {

    @Override
    public Object call(QContext qcontext, Parameters parameters) throws Throwable {
        // 返回 Math.PI 的值
        return Math.PI;
    }
}
