package net.iofun.mdis.framework.qlexpress.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * QLExpress 配置属性
 * 参考文档：https://github.com/alibaba/QLExpress
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties
public class QLExpressProperties {

    /**
     * 是否启用高精度计算
     * 参考：https://github.com/alibaba/QLExpress?tab=readme-ov-file#高精度计算
     */
    private boolean preciseEnabled = true;
    
    /**
     * 是否启用表达式缓存
     * 参考：https://github.com/alibaba/QLExpress?tab=readme-ov-file#表达式缓存
     */
    private boolean cacheEnabled = true;
    
    /**
     * 缓存大小，默认1024
     */
    private int cacheSize = 1024;
    
    /**
     * 执行超时时间（毫秒），0表示不限制
     */
    private long timeoutMs = 1000;
    
    /**
     * 是否启用短路逻辑
     */
    private boolean shortCircuitDisabled = false;

}
