package net.iofun.mdis.framework.qlexpress.function.math;

import com.alibaba.qlexpress4.runtime.Parameters;
import com.alibaba.qlexpress4.runtime.QContext;
import com.alibaba.qlexpress4.runtime.function.CustomFunction;
import net.iofun.mdis.framework.common.util.number.NumberUtils;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * POW 函数，计算幂
 * 参考, AviatorScript 实现
 * https://github.com/killme2008/aviatorscript/blob/master/src/main/java/com/googlecode/aviator/runtime/function/math/MathPowFunction.java
 *
 * <AUTHOR>
 */
public class PowFunction implements CustomFunction {

    @Override
    public Object call(QContext qcontext, Parameters parameters) {
        Number base = (Number) parameters.getValue(0);
        Number exponent = (Number) parameters.getValue(1);
        if (base instanceof BigInteger) {
            return ((BigInteger) base).pow(exponent.intValue());
        } else if (base instanceof BigDecimal) {
            return new BigDecimal(base.toString(), NumberUtils.DECIMAL_PRECISION).pow(exponent.intValue());
        } else {
            return Math.pow(base.doubleValue(), exponent.doubleValue());
        }
    }
}
