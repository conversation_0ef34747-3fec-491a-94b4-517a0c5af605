package net.iofun.mdis.framework.qlexpress.core;

import com.alibaba.qlexpress4.Express4Runner;
import com.alibaba.qlexpress4.InitOptions;
import com.alibaba.qlexpress4.QLOptions;
import com.alibaba.qlexpress4.QLResult;
import com.alibaba.qlexpress4.aparser.ImportManager;
import com.alibaba.qlexpress4.security.QLSecurityStrategy;
import lombok.extern.slf4j.Slf4j;
import net.iofun.mdis.framework.common.util.number.NumberUtils;
import net.iofun.mdis.framework.qlexpress.config.QLExpressProperties;
import net.iofun.mdis.framework.qlexpress.function.math.PiFunction;
import net.iofun.mdis.framework.qlexpress.model.ComputationResult;
import net.iofun.mdis.framework.qlexpress.model.ExpressionRequest;
import net.iofun.mdis.framework.qlexpress.model.ExpressionVariable;
import org.springframework.beans.factory.InitializingBean;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * QLExpress 表达式计算器
 * 参考文档：https://github.com/alibaba/QLExpress
 *
 * <AUTHOR>
 */
@Slf4j
public class QLExpressCalculator implements InitializingBean {
    
    private final QLExpressProperties properties;
    private Express4Runner runner;
    private QLOptions customQLOptions;
    
    public QLExpressCalculator(QLExpressProperties properties) {
        this.properties = properties;
    }

    /**
     * 初始化 QLExpress
     * InitOptions 是创建 Express4Runner 时的默认配置, 可以在这里配置;
     * QLOptions 是每个 Express4Runner 实例运行时的配置, 需要在每次执行时作为变量传入;
     *
     * TraceExpression 是 InitOptions, QLOptions 中都有的设置, 需要同时打开才有效;
     *
     * InitOptions - InterpolationMode, 插值管理方式, Script-作为表达式执行, variable-作为变量名引用, disable-关闭插值功能
     * InitOptions - SecurityStrategy, 安全策略, isolation < whitelist < blacklist < open;
     */
    @Override
    public void afterPropertiesSet() throws Exception {

        // 定制 InitOptions; 这里沿用了默认设置,只是为了演示如何定制;
        InitOptions customInitOptions = InitOptions.builder().securityStrategy(QLSecurityStrategy.isolation()).build();

        // 启动时, 查看初始化配置情况
        for (ImportManager.QLImport item : customInitOptions.getDefaultImport()) {
            log.info("======== QLExpress customInitOptions defaultImport: {}, {}", item.getScope(), item.getTarget());
        }
        log.info("======== QLExpress customInitOptions securityStrategy: {}", customInitOptions.getSecurityStrategy());
        log.info("======== QLExpress customInitOptions interpolationMode: {}", customInitOptions.getInterpolationMode());
        log.info("======== QLExpress customInitOptions traceExpression: {}", customInitOptions.isTraceExpression());

        this.runner = new Express4Runner(customInitOptions);

        // 定制 QLOptions, 用于每个表达式执行时的配置;
        this.customQLOptions = QLOptions.builder()
                .cache(properties.isCacheEnabled()) // 开启缓存
                .precise(properties.isPreciseEnabled()) // 开启高精度计算
                .timeoutMillis(properties.getTimeoutMs()) // 设置超时时间
                .build();

        // 查看定制的 QLOptions
        log.info("======== QLExpress customQLOptions cache: {}", customQLOptions.isCache());
        log.info("======== QLExpress customQLOptions precise: {}", customQLOptions.isPrecise());
        log.info("======== QLExpress customQLOptions timeoutMillis: {}", customQLOptions.getTimeoutMillis());

        // 注册自定义函数
        registerCustomFunctions();
    }
    
    /**
     * 注册自定义函数
     */
    private void registerCustomFunctions() throws Exception {
        // 注册 Pi 函数
        runner.addFunction("Pi", new PiFunction());

        // 设置别名 PI
        runner.addAlias("PI", "Pi");

        log.info("QLExpress custom functions registered: Pi, PI");
    }
    
    /**
     * 计算表达式
     */
    public ComputationResult calculate(ExpressionRequest request) {
        if (request == null || request.getExpression() == null || request.getExpression().trim().isEmpty()) {
            return ComputationResult.failure("表达式不能为空");
        }

        try {
            // 准备变量环境
            Map<String, Object> context = new HashMap<>();
            if (request.getVariables() != null) {
                for (ExpressionVariable variable : request.getVariables()) {
                    if (variable.getName() != null && variable.getValue() != null) {
                        context.put(variable.getName(), variable.getValue());
                    }
                }
            }

            // 执行表达式
            QLResult result = runner.execute(request.getExpression(), context, customQLOptions);

            // 转换结果为 BigDecimal
            BigDecimal resultValue = convertToBigDecimal(result.getResult());

            log.debug("Expression calculated successfully: {} = {}", request.getExpression(), resultValue);
            return ComputationResult.success(resultValue);

        } catch (Exception e) {
            log.error("Failed to calculate expression: {}", request.getExpression(), e);
            return ComputationResult.failure("计算失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证表达式的有效性
     */
    public Boolean validateExpression(String expr) {
        if (expr == null || expr.trim().isEmpty()) {
            log.warn("Expression is null or empty");
            return false;
        }

        try {
            // 尝试解析表达式来验证其有效性
            runner.parseToSyntaxTree(expr);
            log.debug("Expression validation successful: {}", expr);
            return true;
        } catch (Exception e) {
            log.error("Expression validation failed: {}", expr, e);
            return false;
        }
    }
    
    /**
     * 将结果转换为 BigDecimal
     */
    private BigDecimal convertToBigDecimal(Object result) {
        if (result == null) {
            return BigDecimal.ZERO;
        }
        
        if (result instanceof BigDecimal) {
            return ((BigDecimal) result).round(NumberUtils.DECIMAL_PRECISION);
        } else if (result instanceof Number) {
            return new BigDecimal(result.toString(), NumberUtils.DECIMAL_PRECISION);
        } else {
            return new BigDecimal(result.toString(), NumberUtils.DECIMAL_PRECISION);
        }
    }
}
