<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>net.iofun</groupId>
        <artifactId>mdis-framework</artifactId>
        <version>2.4.0-snapshot</version>
    </parent>

    <artifactId>mdis-spring-boot-starter-qlexpress</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>
        QLExpress4 阿里的嵌入式Java动态脚本工具, 解释执行
    </description>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 通用工具 -->
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-common</artifactId>
        </dependency>

        <!-- QLExpress4 核心依赖 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>qlexpress4</artifactId>
        </dependency>

        <!-- Spring Boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>