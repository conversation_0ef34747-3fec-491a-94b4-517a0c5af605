package net.iofun.mdis.framework.tenant.config;

import net.iofun.mdis.framework.common.biz.system.tenant.TenantCommonApi;
import net.iofun.mdis.framework.common.enums.WebFilterOrderEnum;
import net.iofun.mdis.framework.tenant.core.db.TenantMongoDBHandler;
import net.iofun.mdis.framework.redis.config.MdisCacheProperties;
import net.iofun.mdis.framework.tenant.core.aop.TenantIgnoreAspect;
import net.iofun.mdis.framework.tenant.core.job.TenantJobAspect;
import net.iofun.mdis.framework.tenant.core.redis.TenantRedisCacheManager;
import net.iofun.mdis.framework.tenant.core.security.TenantSecurityWebFilter;
import net.iofun.mdis.framework.tenant.core.service.TenantFrameworkService;
import net.iofun.mdis.framework.tenant.core.service.TenantFrameworkServiceImpl;
import net.iofun.mdis.framework.tenant.core.web.TenantContextWebFilter;
import net.iofun.mdis.framework.tenant.core.util.TenantUtilsInitializer;
import net.iofun.mdis.framework.web.config.WebProperties;
import net.iofun.mdis.framework.web.core.handler.GlobalExceptionHandler;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.BatchStrategies;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Objects;

@AutoConfiguration
@ConditionalOnProperty(prefix = "mdis.tenant", value = "enable", matchIfMissing = true) // 允许使用 mdis.tenant.enable=false 禁用多租户
@EnableConfigurationProperties(TenantProperties.class)
public class MdisTenantMongoAutoConfiguration {

    @Bean
    public TenantFrameworkService tenantFrameworkService(TenantCommonApi tenantApi) {
        return new TenantFrameworkServiceImpl(tenantApi);
    }

    // ========== AOP ==========

    @Bean
    public TenantIgnoreAspect tenantIgnoreAspect() {
        return new TenantIgnoreAspect();
    }

    // ========== DB ==========

    @Bean
    public TenantMongoDBHandler tenantMongoDBHandler(TenantProperties properties) {
        return new TenantMongoDBHandler(properties);
    }

    @Bean
    public TenantUtilsInitializer tenantUtilsInitializer(TenantMongoDBHandler tenantMongoDBHandler) {
        return new TenantUtilsInitializer(tenantMongoDBHandler);
    }

    // ========== WEB ==========

    @Bean
    public FilterRegistrationBean<TenantContextWebFilter> tenantContextWebFilter() {
        FilterRegistrationBean<TenantContextWebFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new TenantContextWebFilter());
        registrationBean.setOrder(WebFilterOrderEnum.TENANT_CONTEXT_FILTER);
        return registrationBean;
    }

    // ========== Security ==========

    @Bean
    public FilterRegistrationBean<TenantSecurityWebFilter> tenantSecurityWebFilter(TenantProperties tenantProperties,
                                                                                   WebProperties webProperties,
                                                                                   GlobalExceptionHandler globalExceptionHandler,
                                                                                   TenantFrameworkService tenantFrameworkService) {
        FilterRegistrationBean<TenantSecurityWebFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new TenantSecurityWebFilter(tenantProperties, webProperties,
                globalExceptionHandler, tenantFrameworkService));
        registrationBean.setOrder(WebFilterOrderEnum.TENANT_SECURITY_FILTER);
        return registrationBean;
    }

    // ========== Job ==========

    @Bean
    public TenantJobAspect tenantJobAspect(TenantFrameworkService tenantFrameworkService) {
        return new TenantJobAspect(tenantFrameworkService);
    }

    // ========== Redis ==========

    @Bean
    @Primary // 引入租户时，tenantRedisCacheManager 为主 Bean
    public RedisCacheManager tenantRedisCacheManager(RedisTemplate<String, Object> redisTemplate,
                                                     RedisCacheConfiguration redisCacheConfiguration,
                                                     MdisCacheProperties mdisCacheProperties,
                                                     TenantProperties tenantProperties) {
        // 创建 RedisCacheWriter 对象
        RedisConnectionFactory connectionFactory = Objects.requireNonNull(redisTemplate.getConnectionFactory());
        RedisCacheWriter cacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(connectionFactory,
                BatchStrategies.scan(mdisCacheProperties.getRedisScanBatchSize()));
        // 创建 TenantRedisCacheManager 对象
        return new TenantRedisCacheManager(cacheWriter, redisCacheConfiguration, tenantProperties.getIgnoreCaches());
    }

}
