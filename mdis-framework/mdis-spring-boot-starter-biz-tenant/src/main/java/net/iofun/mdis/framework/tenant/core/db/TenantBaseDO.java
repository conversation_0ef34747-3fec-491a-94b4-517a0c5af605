//package net.iofun.mdis.framework.tenant.core.db;
//
//import net.iofun.mdis.framework.mybatis.core.dataobject.BaseDO;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
///**
// * 拓展多租户的 BaseDO 基类
// *
// * <AUTHOR>
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public abstract class TenantBaseDO extends BaseDO {
//
//    /**
//     * 多租户编号
//     */
//    private Long tenantId;
//
//}
