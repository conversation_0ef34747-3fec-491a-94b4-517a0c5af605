package net.iofun.mdis.framework.tenant.core.db;

import cn.hutool.core.collection.CollUtil;
import net.iofun.mdis.framework.tenant.config.TenantProperties;
import net.iofun.mdis.framework.tenant.core.context.TenantContextHolder;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class TenantMongoDBHandler {
    private final Set<String> ignoreTables = new HashSet<>();

    public TenantMongoDBHandler(TenantProperties properties) {
        properties.getIgnoreTables().forEach(table -> {
            ignoreTables.add(table);
        });
    }

    public String getTenantId() {
        return TenantContextHolder.getRequiredTenantId();
    }

    public boolean ignoreTable(String tableName) {
        return TenantContextHolder.isIgnore() // 情况一，全局忽略多租户
            || CollUtil.contains(ignoreTables, tableName); // 情况二，忽略多租户的表
    }
}
