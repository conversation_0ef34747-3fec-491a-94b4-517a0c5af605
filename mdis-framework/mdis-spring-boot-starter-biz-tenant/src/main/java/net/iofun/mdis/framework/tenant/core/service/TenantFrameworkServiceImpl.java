package net.iofun.mdis.framework.tenant.core.service;

import net.iofun.mdis.framework.common.biz.system.tenant.TenantCommonApi;
import net.iofun.mdis.framework.common.exception.ServiceException;
import net.iofun.mdis.framework.common.util.cache.CacheUtils;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;

import java.time.Duration;
import java.util.List;

/**
 * Tenant 框架 Service 实现类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class TenantFrameworkServiceImpl implements TenantFrameworkService {

    private static final ServiceException SERVICE_EXCEPTION_NULL = new ServiceException();

    private final TenantCommonApi tenantApi;

    /**
     * 针对 {@link #getTenantIds()} 的缓存
     */
    private final LoadingCache<Object, List<String>> getTenantIdsCache = CacheUtils.buildAsyncReloadingCache(
            Duration.ofMinutes(1L), // 过期时间 1 分钟
            new CacheLoader<Object, List<String>>() {

                @Override
                public List<String> load(Object key) {
                    return tenantApi.getTenantIdList();
                }

            });

    /**
     * 针对 {@link #validTenant(String)} 的缓存
     */
    private final LoadingCache<String, ServiceException> validTenantCache = CacheUtils.buildAsyncReloadingCache(
            Duration.ofMinutes(1L), // 过期时间 1 分钟
            new CacheLoader<String, ServiceException>() {

                @Override
                public ServiceException load(String strId) {
                    try {
                        tenantApi.validateTenant(strId);
                        return SERVICE_EXCEPTION_NULL;
                    } catch (ServiceException ex) {
                        return ex;
                    }
                }

            });

    @Override
    @SneakyThrows
    public List<String> getTenantIds() {
        return getTenantIdsCache.get(Boolean.TRUE);
    }

    @Override
    public void validTenant(String strId) {
        ServiceException serviceException = validTenantCache.getUnchecked(strId);
        if (serviceException != SERVICE_EXCEPTION_NULL) {
            throw serviceException;
        }
    }

}
