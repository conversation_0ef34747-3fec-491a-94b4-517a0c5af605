package net.iofun.mdis.framework.tenant.core.util;

import net.iofun.mdis.framework.tenant.core.db.TenantMongoDBHandler;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * TenantUtils 初始化器
 * 用于将 TenantMongoDBHandler 注入到 TenantUtils 的静态字段中
 *
 * <AUTHOR>
 */
@Component
public class TenantUtilsInitializer implements InitializingBean {

    private final TenantMongoDBHandler tenantMongoDBHandler;

    public TenantUtilsInitializer(TenantMongoDBHandler tenantMongoDBHandler) {
        this.tenantMongoDBHandler = tenantMongoDBHandler;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 将 TenantMongoDBHandler 设置到 TenantUtils 的静态字段中
        TenantUtils utils = new TenantUtils();
        utils.setTenantMongoDBHandler(tenantMongoDBHandler);
    }
}
