package net.iofun.mdis.framework.apilog.config;

import net.iofun.mdis.framework.apilog.core.filter.ApiAccessLogFilter;
import net.iofun.mdis.framework.apilog.core.interceptor.ApiAccessLogInterceptor;
import net.iofun.mdis.framework.apilog.core.service.ApiAccessLogFrameworkService;
import net.iofun.mdis.framework.apilog.core.service.ApiAccessLogFrameworkServiceImpl;
import net.iofun.mdis.framework.apilog.core.service.ApiErrorLogFrameworkService;
import net.iofun.mdis.framework.apilog.core.service.ApiErrorLogFrameworkServiceImpl;
import net.iofun.mdis.framework.common.enums.WebFilterOrderEnum;
import net.iofun.mdis.framework.web.config.WebProperties;
import net.iofun.mdis.framework.web.config.MdisWebAutoConfiguration;
import net.iofun.mdis.framework.common.biz.infra.logger.ApiAccessLogCommonApi;
import net.iofun.mdis.framework.common.biz.infra.logger.ApiErrorLogCommonApi;
import jakarta.servlet.Filter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@AutoConfiguration(after = MdisWebAutoConfiguration.class)
public class MdisApiLogAutoConfiguration implements WebMvcConfigurer {

    @Bean
    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    public ApiAccessLogFrameworkService apiAccessLogFrameworkService(ApiAccessLogCommonApi apiAccessLogApi) {
        return new ApiAccessLogFrameworkServiceImpl(apiAccessLogApi);
    }

    @Bean
    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    public ApiErrorLogFrameworkService apiErrorLogFrameworkService(ApiErrorLogCommonApi apiErrorLogApi) {
        return new ApiErrorLogFrameworkServiceImpl(apiErrorLogApi);
    }

    /**
     * 创建 ApiAccessLogFilter Bean，记录 API 请求日志
     */
    @Bean
    @ConditionalOnProperty(prefix = "mdis.access-log", value = "enable", matchIfMissing = true) // 允许使用 mdis.access-log.enable=false 禁用访问日志
    public FilterRegistrationBean<ApiAccessLogFilter> apiAccessLogFilter(WebProperties webProperties,
                                                                         @Value("${spring.application.name}") String applicationName,
                                                                         ApiAccessLogFrameworkService apiAccessLogFrameworkService) {
        ApiAccessLogFilter filter = new ApiAccessLogFilter(webProperties, applicationName, apiAccessLogFrameworkService);
        return createFilterBean(filter, WebFilterOrderEnum.API_ACCESS_LOG_FILTER);
    }

    private static <T extends Filter> FilterRegistrationBean<T> createFilterBean(T filter, Integer order) {
        FilterRegistrationBean<T> bean = new FilterRegistrationBean<>(filter);
        bean.setOrder(order);
        return bean;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new ApiAccessLogInterceptor());
    }

}
