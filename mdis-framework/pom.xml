<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>mdis</artifactId>
        <groupId>net.iofun</groupId>
        <version>${revision}</version>
    </parent>
    <packaging>pom</packaging>
    <modules>
        <module>mdis-common</module>
<!--        <module>mdis-spring-boot-starter-mybatis</module>-->
        <module>mdis-spring-boot-starter-redis</module>
        <module>mdis-spring-boot-starter-web</module>
        <module>mdis-spring-boot-starter-security</module>
        <module>mdis-spring-boot-starter-websocket</module>

        <module>mdis-spring-boot-starter-monitor</module>
        <module>mdis-spring-boot-starter-protection</module>
        <module>mdis-spring-boot-starter-job</module>
        <module>mdis-spring-boot-starter-mq</module>

        <module>mdis-spring-boot-starter-excel</module>
        <module>mdis-spring-boot-starter-test</module>

        <module>mdis-spring-boot-starter-biz-tenant</module>
<!--        <module>mdis-spring-boot-starter-biz-data-permission</module>-->
        <module>mdis-spring-boot-starter-biz-ip</module>
        <module>mdis-spring-boot-starter-mongo</module>
        <module>mdis-spring-boot-starter-datapermission</module>
        <module>mdis-spring-boot-starter-aviatorscript</module>
        <module>mdis-spring-boot-starter-qlexpress</module>
    </modules>

    <artifactId>mdis-framework</artifactId>
    <description>
        该包是技术组件，每个子包，代表一个组件。每个组件包括两部分：
            1. core 包：是该组件的核心封装
            2. config 包：是该组件基于 Spring 的配置

        技术组件，也分成两类：
            1. 框架组件：和我们熟悉的 MyBatis、Redis 等等的拓展
            2. 业务组件：和业务相关的组件的封装，例如说数据字典、操作日志等等。
        如果是业务组件，Maven 名字会包含 biz
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

</project>
