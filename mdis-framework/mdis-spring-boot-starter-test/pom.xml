<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>net.iofun</groupId>
        <artifactId>mdis-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>mdis-spring-boot-starter-test</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>测试组件，用于单元测试、集成测试</description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <dependencies>
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-common</artifactId>
        </dependency>

        <!-- DB 相关 -->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-spring-boot-starter-mybatis</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-core</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId> <!-- 单元测试，我们采用 H2 作为数据库 -->
            <artifactId>h2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.fppt</groupId> <!-- 单元测试，我们采用内嵌的 Redis 数据库 -->
            <artifactId>jedis-mock</artifactId>
        </dependency>

        <dependency>
            <groupId>uk.co.jemos.podam</groupId> <!-- 单元测试，随机生成 POJO 类 -->
            <artifactId>podam</artifactId>
        </dependency>
    </dependencies>
</project>
