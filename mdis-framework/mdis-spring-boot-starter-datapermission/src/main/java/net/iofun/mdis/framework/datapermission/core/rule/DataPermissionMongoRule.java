package net.iofun.mdis.framework.datapermission.core.rule;

import java.util.Set;

import org.springframework.data.mongodb.core.query.Criteria;

import net.iofun.mdis.framework.security.core.LoginUser;

/**
 * 具体数据权限的规则实现, 跟业务相关, 所以放在不同的业务模块中实现,
 * 业务模块需要实现该接口, 并在实现类上添加 @Component 注解, 以便被 Spring 扫描到
 *
 * <AUTHOR>
 */
public interface DataPermissionMongoRule {

    /**
     * 返回 表名
     * @return
     */
    Set<String> getTableNames();

    /**
     * 根据 表名 生成对应的 Criteria
     * @param tableName
     * @param loginUser
     * @return
     */
    Criteria getCriteria(String tableName, LoginUser loginUser);

}
