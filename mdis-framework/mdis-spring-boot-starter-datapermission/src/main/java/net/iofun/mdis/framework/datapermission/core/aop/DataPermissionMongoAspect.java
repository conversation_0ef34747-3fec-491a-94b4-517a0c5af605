package net.iofun.mdis.framework.datapermission.core.aop;

import net.iofun.mdis.framework.common.entity.BaseEntity;
import net.iofun.mdis.framework.datapermission.core.annotation.DataPermissionMongo;
import net.iofun.mdis.framework.datapermission.core.rule.DataPermissionMongoRule;
import net.iofun.mdis.framework.datapermission.core.util.DataPermissionMongoUtils;
import net.iofun.mdis.framework.security.core.LoginUser;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.context.ApplicationContext;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;
import jakarta.annotation.Resource;

import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;

/**
 * <AUTHOR>
 */
@Aspect
public class DataPermissionMongoAspect {

    @Resource
    private ApplicationContext applicationContext;

    @Around("@annotation(dataPermissionMongo)")
    public Object applyDataPermission(ProceedingJoinPoint joinPoint, DataPermissionMongo dataPermissionMongo) throws Throwable {
        try {
            // 1. 获取LoginUser
            LoginUser loginUser = getLoginUser();
            DataPermissionMongoUtils.validLoginUser(loginUser);
            // 2. 获取 Domain 类名称
            Class<? extends BaseEntity> domainClass = dataPermissionMongo.domainClass();
            String tableName = domainClass.getAnnotation(Document.class).value();
            // 3. 获取注解上的数据权限规则类
            Class<? extends DataPermissionMongoRule> ruleClass= dataPermissionMongo.applyRule();
            // 把注解传入的数据权限规则类实例化
            DataPermissionMongoRule dataPermissionMongoRule = applicationContext.getBean(ruleClass);
            // 获取规则
            Criteria criteria = dataPermissionMongoRule.getCriteria(tableName, loginUser);
            // 不为空, 数据规则map(key: domainClass, value: criterias), 放入ctx
            if (!(criteria == null)) {
                DataPermissionMongoContext.addCriteria(domainClass, criteria);
            }
            // 4. 执行原方法
            return joinPoint.proceed();
        } finally {
            DataPermissionMongoContext.clear();
        }
    }
}
