package net.iofun.mdis.framework.datapermission.core.util;

import net.iofun.mdis.framework.security.core.LoginUser;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.framework.datapermission.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 */
public class DataPermissionMongoUtils {

    public static void validLoginUser(LoginUser loginUser) {
        if (null == loginUser) {
            throw exception(LOGIN_USER_NO_EXISTS);
        }
    }

    public static String  validateAndGetDeptId(LoginUser loginUser) {
        String deptId;
        try{
            deptId = loginUser.getInfo().get(LoginUser.INFO_KEY_DEPT_ID);
        }catch(NullPointerException e){
            throw exception(LOGIN_USER_OR_DEPT_ID_NOT_EXISTS);
        }
        return deptId;
    }
}
