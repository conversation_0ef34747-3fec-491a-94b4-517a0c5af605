package net.iofun.mdis.framework.datapermission.core.annotation;

import java.lang.annotation.*;

import net.iofun.mdis.framework.common.entity.BaseEntity;
import net.iofun.mdis.framework.datapermission.core.rule.DataPermissionMongoRule;
/**
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataPermissionMongo {
    Class<? extends BaseEntity> domainClass();
    // String[] fields();

    /**
     * 生效的数据权限规则
     */
    Class<? extends DataPermissionMongoRule> applyRule();
}
