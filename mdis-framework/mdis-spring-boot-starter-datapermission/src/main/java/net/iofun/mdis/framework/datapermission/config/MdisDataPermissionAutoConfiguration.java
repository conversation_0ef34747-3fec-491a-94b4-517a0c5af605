package net.iofun.mdis.framework.datapermission.config;

import net.iofun.mdis.framework.datapermission.core.aop.DataPermissionMongoAspect;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

/**
 * 数据权限自动配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration
public class MdisDataPermissionAutoConfiguration {

    // 注册数据权限 AOP
    @Bean
    public DataPermissionMongoAspect dataPermissionMongoAspect() {
        return new DataPermissionMongoAspect();
    }
}
