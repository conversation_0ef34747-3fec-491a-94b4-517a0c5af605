package net.iofun.mdis.framework.datapermission.enums;

import net.iofun.mdis.framework.common.exception.ErrorCode;

/**
 * Cost基础模块 错误码枚举类
 *
 * 使用 1-015-000-000 段
 * <AUTHOR>
 */
public interface ErrorCodeConstants {

    // ========== starter mongo ============
    ErrorCode LOGIN_USER_NO_EXISTS = new ErrorCode(1_090_001_002, "数据权限中的登录用户无法获取");
    ErrorCode LOGIN_USER_OR_DEPT_ID_NOT_EXISTS = new ErrorCode(1_090_001_003, "当前用户或者其部门编号不存在");

}
