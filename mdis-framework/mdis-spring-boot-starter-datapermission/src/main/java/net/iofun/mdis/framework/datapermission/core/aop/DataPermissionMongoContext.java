package net.iofun.mdis.framework.datapermission.core.aop;

import com.alibaba.ttl.TransmittableThreadLocal;
import org.springframework.data.mongodb.core.query.Criteria;

import java.util.*;

/**
 * <AUTHOR>
 */
public class DataPermissionMongoContext {

    private static final ThreadLocal<Map<Class<?>, List<Criteria>>> CONTEXT = new TransmittableThreadLocal<>();

    public static void addCriteria(Class<?> domainClass, Criteria criteria) {
        Map<Class<?>, List<Criteria>> criteriaMap = CONTEXT.get();
        if (criteriaMap == null) {
            criteriaMap = new HashMap<>();
            CONTEXT.set(criteriaMap);
        }
         criteriaMap.computeIfAbsent(domainClass, k -> new ArrayList<>()).add(criteria);
    }

    public static void clear() {
        CONTEXT.remove();
    }

    public static List<Criteria> getCriterias(Class<?> domainClass) {
        Map<Class<?>, List<Criteria>> criteriaMap = CONTEXT.get();
        return criteriaMap != null ? criteriaMap.getOrDefault(domainClass, Collections.emptyList()) : Collections.emptyList();
    }
}
