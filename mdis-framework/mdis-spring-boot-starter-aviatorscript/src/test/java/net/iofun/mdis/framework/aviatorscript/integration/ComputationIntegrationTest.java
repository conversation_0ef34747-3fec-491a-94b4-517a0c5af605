package net.iofun.mdis.framework.aviatorscript.integration;

import net.iofun.mdis.framework.aviatorscript.config.AviatorScriptAutoConfiguration;
import net.iofun.mdis.framework.aviatorscript.core.ExpressionCalculator;
import net.iofun.mdis.framework.common.vo.computation.ComputationResult;
import net.iofun.mdis.framework.common.vo.computation.ExpressionRequest;
import net.iofun.mdis.framework.common.vo.computation.ExpressionVariable;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AviatorScript 集成测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = {AviatorScriptAutoConfiguration.class})
@SpringJUnitConfig
class ComputationIntegrationTest {

    @Autowired
    private ExpressionCalculator calculator;

    @Test
    void testBasicCalculation() {
        // 测试基本的加法运算
        ExpressionRequest request = ExpressionRequest.builder()
                .expression("price + tax")
                .variables(Arrays.asList(
                        ExpressionVariable.builder().name("price").value(new BigDecimal("100.50")).build(),
                        ExpressionVariable.builder().name("tax").value(new BigDecimal("8.25")).build()
                ))
                .build();

        ComputationResult result = calculator.calculate(request);

        assertTrue(result.getSuccess());
        assertEquals(new BigDecimal("108.75"), result.getResult());
    }

    @Test
    void testComplexExpression() {
        // 测试复杂表达式：(价格 + 税费) * 数量 - 折扣
        ExpressionRequest request = ExpressionRequest.builder()
                .expression("(price + tax) * quantity - discount")
                .variables(Arrays.asList(
                        ExpressionVariable.builder().name("price").value(new BigDecimal("100.00")).build(),
                        ExpressionVariable.builder().name("tax").value(new BigDecimal("10.00")).build(),
                        ExpressionVariable.builder().name("quantity").value(new BigDecimal("3")).build(),
                        ExpressionVariable.builder().name("discount").value(new BigDecimal("25.00")).build()
                ))
                .build();

        ComputationResult result = calculator.calculate(request);

        assertTrue(result.getSuccess());
        assertEquals(new BigDecimal("305.00"), result.getResult());
    }

    @Test
    void testHighPrecisionCalculation() {
        // 测试高精度计算
        ExpressionRequest request = ExpressionRequest.builder()
                .expression("a * b")
                .variables(Arrays.asList(
                        ExpressionVariable.builder().name("a").value(new BigDecimal("123.456789123456789")).build(),
                        ExpressionVariable.builder().name("b").value(new BigDecimal("987.654321987654321")).build()
                ))
                .build();

        ComputationResult result = calculator.calculate(request);

        assertTrue(result.getSuccess());
        assertNotNull(result.getResult());
        // 验证结果包含高精度计算
        assertTrue(result.getResult().scale() > 10);
    }

    @Test
    void testErrorHandling() {
        // 测试错误处理：未定义变量
        ExpressionRequest request = ExpressionRequest.builder()
                .expression("undefined_var + 10")
                .variables(Arrays.asList())
                .build();

        ComputationResult result = calculator.calculate(request);

        assertFalse(result.getSuccess());
        assertNotNull(result.getErrorMessage());
    }

    @Test
    void testEmptyExpression() {
        // 测试空表达式
        ExpressionRequest request = ExpressionRequest.builder()
                .expression("")
                .variables(Arrays.asList())
                .build();

        ComputationResult result = calculator.calculate(request);

        assertFalse(result.getSuccess());
        assertEquals("表达式不能为空", result.getErrorMessage());
    }
}