package net.iofun.mdis.framework.aviatorscript.core;

import net.iofun.mdis.framework.aviatorscript.config.AviatorScriptProperties;
import net.iofun.mdis.framework.common.vo.computation.ComputationResult;
import net.iofun.mdis.framework.common.vo.computation.ExpressionRequest;
import net.iofun.mdis.framework.common.vo.computation.ExpressionVariable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ExpressionCalculator 测试类
 *
 * <AUTHOR>
 */
class ExpressionCalculatorTest {

    private ExpressionCalculator calculator;

    @BeforeEach
    void setUp() {
        AviatorScriptProperties properties = new AviatorScriptProperties();
        properties.setEnabled(true);
        properties.setBigDecimalEnabled(true);

        calculator = new ExpressionCalculator(properties);
        try {
            calculator.afterPropertiesSet();
        } catch (Exception e) {
            fail("Failed to initialize calculator", e);
        }
    }

    @Test
    void testSimpleAddition() {
        ExpressionRequest request = ExpressionRequest.builder()
                .expression("a + b")
                .variables(Arrays.asList(
                        ExpressionVariable.builder().name("a").value(new BigDecimal("10.5")).build(),
                        ExpressionVariable.builder().name("b").value(new BigDecimal("20.3")).build()
                ))
                .build();

        ComputationResult result = calculator.calculate(request);

        assertTrue(result.getSuccess());
        assertEquals(new BigDecimal("30.8"), result.getResult());
    }

    @Test
    void testComplexExpression() {
        ExpressionRequest request = ExpressionRequest.builder()
                .expression("(a + b) * c / d")
                .variables(Arrays.asList(
                        ExpressionVariable.builder().name("a").value(new BigDecimal("10")).build(),
                        ExpressionVariable.builder().name("b").value(new BigDecimal("20")).build(),
                        ExpressionVariable.builder().name("c").value(new BigDecimal("3")).build(),
                        ExpressionVariable.builder().name("d").value(new BigDecimal("2")).build()
                ))
                .build();

        ComputationResult result = calculator.calculate(request);

        assertTrue(result.getSuccess());
        assertEquals(new BigDecimal("45"), result.getResult());
    }

    @Test
    void testEmptyExpression() {
        ExpressionRequest request = ExpressionRequest.builder()
                .expression("")
                .variables(Arrays.asList())
                .build();

        ComputationResult result = calculator.calculate(request);

        assertFalse(result.getSuccess());
        assertNotNull(result.getErrorMessage());
    }

    @Test
    void testHighPrecisionCalculation() {
        ExpressionRequest request = ExpressionRequest.builder()
                .expression("a * b")
                .variables(Arrays.asList(
                        ExpressionVariable.builder().name("a").value(new BigDecimal("123.456789")).build(),
                        ExpressionVariable.builder().name("b").value(new BigDecimal("987.654321")).build()
                ))
                .build();

        ComputationResult result = calculator.calculate(request);

        assertTrue(result.getSuccess());
        // 验证高精度计算结果
        assertEquals(new BigDecimal("121932.631112635269"), result.getResult());
    }
}