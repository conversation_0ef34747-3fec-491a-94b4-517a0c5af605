package net.iofun.mdis.framework.quartz.core.dal.dataobject;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import org.quartz.*;
import org.quartz.Trigger.TriggerState;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.quartz.impl.triggers.SimpleTriggerImpl;
import org.quartz.spi.OperableTrigger;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;

import java.text.ParseException;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Document(collection = "quartz_triggers")
@CompoundIndex(name = "trigger_name_group_idx", def = "{'triggerName': 1, 'triggerGroup': 1}", unique = true)
public class QuartzTriggerDO extends BaseEntity {

    private String triggerName;
    private String triggerGroup;
    private String jobName;
    private String jobGroup;
    private String description;
    private Date nextFireTime;
    private Date prevFireTime;
    private Integer priority;
    private TriggerState triggerState;
    private String type;
    private long startTime;
    private long endTime;
    private String calendarName;
    private int misfireInstr;
    private JobDataMap jobDataMap;
    private String cronExpression;

    public QuartzTriggerDO(OperableTrigger trigger) {
        // setId(new ObjectId()); // <<<< BUG: A new database ID must not be generated here. This is the root cause.

        TriggerKey triggerKey = trigger.getKey();
        this.triggerName = triggerKey.getName();
        this.triggerGroup = triggerKey.getGroup();
        if (trigger.getJobKey() != null) {
            this.jobName = trigger.getJobKey().getName();
            this.jobGroup = trigger.getJobKey().getGroup();
        }
        this.description = trigger.getDescription();
        this.nextFireTime = trigger.getNextFireTime();
        this.prevFireTime = trigger.getPreviousFireTime();
        this.priority = trigger.getPriority();
        this.startTime = trigger.getStartTime().getTime();
        this.endTime = trigger.getEndTime() != null ? trigger.getEndTime().getTime() : -1;
        this.calendarName = trigger.getCalendarName();
        this.misfireInstr = trigger.getMisfireInstruction();
        this.jobDataMap = trigger.getJobDataMap();

        if (trigger instanceof CronTrigger) {
            this.type = "CRON";
            this.cronExpression = ((CronTrigger) trigger).getCronExpression();
        } else if (trigger instanceof SimpleTrigger) {
            this.type = "SIMPLE";
        } else {
            this.type = "UNKNOWN";
        }
    }

    public TriggerKey getTriggerKey() {
        return new TriggerKey(triggerName, triggerGroup);
    }

    public JobKey getJobKey() {
        if (jobName == null || jobGroup == null) {
            return null;
        }
        return new JobKey(jobName, jobGroup);
    }

    public OperableTrigger getTrigger() {
        // Defensive check for nulls, in case of data corruption
        if (triggerName == null || triggerGroup == null) {
            return null;
        }

        if ("CRON".equals(this.type)) {
            CronTriggerImpl cronTrigger = new CronTriggerImpl();
            try {
                cronTrigger.setCronExpression(this.cronExpression);
            } catch (ParseException e) {
                // This should not happen if the cron expression was validated upon storage
                throw new IllegalStateException("Invalid cron expression: " + this.cronExpression, e);
            }
            cronTrigger.setName(this.triggerName);
            cronTrigger.setGroup(this.triggerGroup);
            cronTrigger.setJobKey(this.getJobKey());
            cronTrigger.setDescription(this.description);
            cronTrigger.setNextFireTime(this.nextFireTime);
            cronTrigger.setPreviousFireTime(this.prevFireTime);
            cronTrigger.setPriority(this.priority);
            cronTrigger.setStartTime(new Date(this.startTime));
            if (this.endTime > 0) {
                cronTrigger.setEndTime(new Date(this.endTime));
            }
            cronTrigger.setCalendarName(this.calendarName);
            cronTrigger.setMisfireInstruction(this.misfireInstr);
            return cronTrigger;
        } else if ("SIMPLE".equals(this.type)) {
            SimpleTriggerImpl simpleTrigger = new SimpleTriggerImpl();
            simpleTrigger.setName(this.triggerName);
            simpleTrigger.setGroup(this.triggerGroup);
            simpleTrigger.setJobKey(this.getJobKey());
            simpleTrigger.setDescription(this.description);
            simpleTrigger.setNextFireTime(this.nextFireTime);
            simpleTrigger.setPreviousFireTime(this.prevFireTime);
            simpleTrigger.setPriority(this.priority);
            simpleTrigger.setStartTime(new Date(this.startTime));
            if (this.endTime > 0) {
                simpleTrigger.setEndTime(new Date(this.endTime));
            }
            simpleTrigger.setCalendarName(this.calendarName);
            simpleTrigger.setMisfireInstruction(this.misfireInstr);
            return simpleTrigger;
        }
        // Fallback or throw exception for unknown types
        return null;
    }
} 