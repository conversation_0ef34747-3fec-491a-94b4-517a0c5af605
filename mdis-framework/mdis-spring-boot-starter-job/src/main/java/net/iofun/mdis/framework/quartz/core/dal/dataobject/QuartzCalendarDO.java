package net.iofun.mdis.framework.quartz.core.dal.dataobject;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import org.quartz.Calendar;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@EqualsAndHashCode(callSuper = true)
@Document(collection = "quartz_calendars")
public class QuartzCalendarDO extends BaseEntity {

    @Indexed(unique = true)
    private String name;
    private Calendar calendar;

    public QuartzCalendarDO(String name, Calendar calendar) {
        this.name = name;
        this.calendar = calendar;
    }
} 