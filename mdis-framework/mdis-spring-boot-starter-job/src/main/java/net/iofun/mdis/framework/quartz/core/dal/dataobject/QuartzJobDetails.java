package net.iofun.mdis.framework.quartz.core.dal.dataobject;

import lombok.Data;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Data
@Document("quartz_job_details")
// @CompoundIndex(name = "idx_job_name_group", def = "{'jobName': 1, 'jobGroup': 1}", unique = true)
public class QuartzJobDetails extends BaseEntity {

    private String schedName;       // 调度器名称
    private String jobName;         // 任务名称
    private String jobGroup;        // 任务组
    private String description;     // 描述
    private String jobClassName;    // 任务类名
    private boolean durability;     // 是否持久
    private boolean nonConcurrent;  // 是否不允许并发执行
    private boolean updateData;     // 是否更新数据
    private boolean requestsRecovery; // 是否要求恢复
    private byte[] jobData;         // 任务数据
}