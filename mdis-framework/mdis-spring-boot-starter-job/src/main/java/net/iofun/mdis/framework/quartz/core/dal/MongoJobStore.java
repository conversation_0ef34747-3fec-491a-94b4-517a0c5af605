package net.iofun.mdis.framework.quartz.core.dal;

import cn.hutool.extra.spring.SpringUtil;
import net.iofun.mdis.framework.quartz.core.dal.dataobject.QuartzCalendarDO;
import net.iofun.mdis.framework.quartz.core.dal.dataobject.QuartzJobDO;
import net.iofun.mdis.framework.quartz.core.dal.dataobject.QuartzTriggerDO;
import net.iofun.mdis.framework.quartz.core.enums.JobDataKeyEnum;
import net.iofun.mdis.framework.quartz.core.service.JobLogFrameworkService;

import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.quartz.spi.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * quartz_triggers 表中, 触发器的状态, 有以下几种:
 * 1. NORMAL: 正常状态, 触发器可以正常触发
 * 2. PAUSED: 暂停状态, 触发器不会触发
 * 3. COMPLETE: 完成状态, 触发器已经完成, 不会触发
 * 4. ERROR: 错误状态, 触发器触发失败
 * 5. BLOCKED: 阻塞状态, 触发器被阻塞, 不会触发
 * 6. PAUSED_BLOCKED: 暂停阻塞状态, 触发器被暂停并阻塞, 不会触发;
 * 在定时任务页面, 修改任务,比如开启和暂停, 会更新quartz_triggers表中的相关任务的triggerState状态;
 */

/**
 * <AUTHOR>
 * @comment 使用 MongoDB 作为 Quartz 的 JobStore 实现。
 * @comment 该实现旨在提供一个持久化的、可集群的作业存储解决方案。
 * @comment 注意：由于 Quartz 调度器通过反射创建此类的实例，我们不能直接使用 @Resource 或 @Autowired 来注入依赖。
 * @comment 相反，我们在 {@link #initialize(ClassLoadHelper, SchedulerSignaler)} 方法中，通过 Spring 的 {@code ApplicationContext} 来手动获取所需的 bean，例如 {@code MongoTemplate}。
 */
@Component
public class MongoJobStore implements JobStore {
    private static final String COLLECTION_PREFIX = "quartz_";
    private static final String COLLECTION_JOBS = COLLECTION_PREFIX + "jobs";
    private static final String COLLECTION_TRIGGERS = COLLECTION_PREFIX + "triggers";
    private static final String COLLECTION_CALENDARS = COLLECTION_PREFIX + "calendars";
    private static final String COLLECTION_LOCKS = COLLECTION_PREFIX + "locks";
    private static final String COLLECTION_SCHEDULER_STATE = COLLECTION_PREFIX + "scheduler_state";
    private static final String COLLECTION_FIRED_TRIGGERS = COLLECTION_PREFIX + "fired_triggers";

    // 注意：由于 Quartz 框架自行创建了 MongoJobStore 的实例，所以这里使用 @Resource 注解会失效，需要在 initialize 方法中手动设置。
    private MongoTemplate mongoTemplate;

    private String instanceId;
    private String instanceName;
    private long misfireThreshold;
    private boolean isClustered;
    private long clusterCheckinInterval;
    private SchedulerSignaler signaler;
    private ClassLoadHelper loadHelper;
    private JobLogFrameworkService jobLogFrameworkService;

    public MongoJobStore() {
        // Quartz 实例化时使用的构造函数
    }

    private void createCollections() {
        // 集合将在第一次插入数据时由 MongoDB 自动创建。
        // 如果将来需要，我们可以在这里创建索引。
    }

    // 初始化
    @Override
    public void initialize(ClassLoadHelper loadHelper, SchedulerSignaler signaler) {
        this.loadHelper = loadHelper;
        this.signaler = signaler;
        this.mongoTemplate = SpringUtil.getBean("mongoTemplate", MongoTemplate.class);
        this.jobLogFrameworkService = SpringUtil.getBean(JobLogFrameworkService.class);
        createCollections();
    }

    @Override
    public void schedulerStarted() throws SchedulerException {
        // 未实现
    }

    @Override
    public void schedulerPaused() {
        // 未实现
    }

    @Override
    public void schedulerResumed() {
        // 未实现
    }

    @Override
    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    @Override
    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    @Override
    public boolean isClustered() {
        return isClustered;
    }

    @Override
    public void setThreadPoolSize(int poolSize) {
        // 未使用
    }

    @Override
    public boolean supportsPersistence() {
        return true;
    }

    @Override
    public long getEstimatedTimeToReleaseAndAcquireTrigger() {
        return 0;
    }

    @Override
    public void storeJobAndTrigger(JobDetail newJob, OperableTrigger newTrigger) throws JobPersistenceException {
        storeJob(newJob, false);
        storeTrigger(newTrigger, false);
    }

    @Override
    public void storeJobsAndTriggers(Map<JobDetail, Set<? extends Trigger>> newJobsAndTriggers, boolean replace) throws ObjectAlreadyExistsException, JobPersistenceException {
        for (Map.Entry<JobDetail, Set<? extends Trigger>> entry : newJobsAndTriggers.entrySet()) {
            storeJob(entry.getKey(), replace);
            for (Trigger trigger : entry.getValue()) {
                storeTrigger((OperableTrigger) trigger, replace);
            }
        }
    }

    @Override
    public void storeJob(JobDetail newJob, boolean replace) throws ObjectAlreadyExistsException {
        JobKey jobKey = newJob.getKey();
        QuartzJobDO existingJob = findJob(jobKey);

        if (existingJob != null) {
            if (!replace) {
                throw new ObjectAlreadyExistsException(newJob);
            }
            // 替换时，创建一个新的 DO，但关键是重用现有文档的 _id。
            QuartzJobDO jobToSave = new QuartzJobDO(newJob);
            jobToSave.setId(existingJob.getId());
            jobToSave.setCreateTime(existingJob.getCreateTime()); // 保留原始创建时间
            mongoTemplate.save(jobToSave); // save() 现在将执行 UPDATE
        } else {
            // 新作业，执行插入。ID 为 null，mongo 会自动生成。
            QuartzJobDO jobToSave = new QuartzJobDO(newJob);
            mongoTemplate.save(jobToSave);
        }
    }

    @Override
    public boolean removeJob(JobKey jobKey) {
        return mongoTemplate.remove(jobQuery(jobKey), QuartzJobDO.class).getDeletedCount() > 0;
    }

    @Override
    public boolean removeJobs(List<JobKey> jobKeys) {
        if (jobKeys == null || jobKeys.isEmpty()) {
            return false;
        }
        List<Criteria> orCriteria = jobKeys.stream()
                .map(jobKey -> Criteria.where("jobGroup").is(jobKey.getGroup()).and("jobName").is(jobKey.getName()))
                .collect(Collectors.toList());
        Query query = new Query(new Criteria().orOperator(orCriteria));
        return mongoTemplate.remove(query, QuartzJobDO.class).getDeletedCount() > 0;
    }

    @Override
    public JobDetail retrieveJob(JobKey jobKey) throws JobPersistenceException {
        QuartzJobDO doc = mongoTemplate.findOne(jobQuery(jobKey), QuartzJobDO.class);
        if (doc == null) {
            return null;
        }
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Job> jobClass = (Class<? extends Job>) loadHelper.loadClass(doc.getJobClass());
            return JobBuilder.newJob(jobClass)
                    .withIdentity(doc.getJobKey())
                    .withDescription(doc.getDescription())
                    .usingJobData(doc.getJobDataMap())
                    .storeDurably(doc.isDurability())
                    .requestRecovery(doc.isShouldRecover())
                    .build();
        } catch (ClassNotFoundException e) {
            throw new JobPersistenceException("Could not load job class " + doc.getJobClass(), e);
        }
    }

    @Override
    public void storeTrigger(OperableTrigger newTrigger, boolean replace) throws ObjectAlreadyExistsException {
        TriggerKey triggerKey = newTrigger.getKey();
        QuartzTriggerDO existingTrigger = findTrigger(triggerKey);

        if (existingTrigger != null) {
            if (!replace) {
                throw new ObjectAlreadyExistsException(newTrigger);
            }
            // 替换时，创建一个新的 DO，但关键是重用现有文档的 _id。
            QuartzTriggerDO triggerToSave = new QuartzTriggerDO(newTrigger);
            triggerToSave.setId(existingTrigger.getId());
            triggerToSave.setCreateTime(existingTrigger.getCreateTime()); // 保留原始创建时间
            mongoTemplate.save(triggerToSave); // save() 现在将执行 UPDATE
        } else {
            // 新触发器，执行插入。ID 为 null，mongo 会自动生成。
            QuartzTriggerDO triggerToSave = new QuartzTriggerDO(newTrigger);
            mongoTemplate.save(triggerToSave);
        }
    }

    @Override
    public boolean removeTrigger(TriggerKey triggerKey) {
        return mongoTemplate.remove(triggerQuery(triggerKey), QuartzTriggerDO.class).getDeletedCount() > 0;
    }

    @Override
    public boolean removeTriggers(List<TriggerKey> triggerKeys) {
        if (triggerKeys == null || triggerKeys.isEmpty()) {
            return false;
        }
        List<Criteria> orCriteria = triggerKeys.stream()
                .map(triggerKey -> Criteria.where("triggerGroup").is(triggerKey.getGroup()).and("triggerName").is(triggerKey.getName()))
                .collect(Collectors.toList());
        Query query = new Query(new Criteria().orOperator(orCriteria));
        return mongoTemplate.remove(query, QuartzTriggerDO.class).getDeletedCount() > 0;
    }

    @Override
    public OperableTrigger retrieveTrigger(TriggerKey triggerKey) {
        QuartzTriggerDO trigger = findTrigger(triggerKey);
        return trigger != null ? trigger.getTrigger() : null;
    }

    @Override
    public boolean replaceTrigger(TriggerKey triggerKey, OperableTrigger newTrigger) throws JobPersistenceException {
        try {
            // 根据我们新的 storeTrigger 逻辑，这将正确地更新现有的触发器。
            storeTrigger(newTrigger, true);
            return true;
        } catch (ObjectAlreadyExistsException e) {
            // 当 replaceExisting 为 true 时，不应该发生这种情况。
            throw new JobPersistenceException("替换触发器时出错", e);
        }
    }

    @Override
    public boolean checkExists(JobKey jobKey) {
        return mongoTemplate.exists(jobQuery(jobKey), QuartzJobDO.class);
    }

    @Override
    public boolean checkExists(TriggerKey triggerKey) {
        return mongoTemplate.exists(triggerQuery(triggerKey), QuartzTriggerDO.class);
    }

    @Override
    public void clearAllSchedulingData() {
        mongoTemplate.remove(new Query(), QuartzJobDO.class);
        mongoTemplate.remove(new Query(), QuartzTriggerDO.class);
        mongoTemplate.remove(new Query(), QuartzCalendarDO.class);
    }

    @Override
    public Set<JobKey> getJobKeys(GroupMatcher<JobKey> matcher) {
        return mongoTemplate.find(new Query(createCriteria(matcher, "jobGroup")), QuartzJobDO.class)
                .stream()
                .map(QuartzJobDO::getJobKey)
                .collect(Collectors.toSet());
    }

    @Override
    public List<String> getJobGroupNames() {
        return mongoTemplate.query(QuartzJobDO.class).distinct("jobGroup").as(String.class).all();
    }

    @Override
    public Set<TriggerKey> getTriggerKeys(GroupMatcher<TriggerKey> matcher) {
        return mongoTemplate.find(new Query(createCriteria(matcher, "triggerGroup")), QuartzTriggerDO.class)
                .stream()
                .map(QuartzTriggerDO::getTriggerKey)
                .collect(Collectors.toSet());
    }

    @Override
    public List<String> getTriggerGroupNames() {
        return mongoTemplate.query(QuartzTriggerDO.class).distinct("triggerGroup").as(String.class).all();
    }

    @Override
    public List<OperableTrigger> getTriggersForJob(JobKey jobKey) {
        Query query = new Query(Criteria.where("jobGroup").is(jobKey.getGroup()).and("jobName").is(jobKey.getName()));
        return mongoTemplate.find(query, QuartzTriggerDO.class)
                .stream()
                .map(QuartzTriggerDO::getTrigger)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public Trigger.TriggerState getTriggerState(TriggerKey triggerKey) {
        QuartzTriggerDO doc = findTrigger(triggerKey);
        if (doc == null) {
            return Trigger.TriggerState.NONE;
        }
        return doc.getTriggerState() != null ? doc.getTriggerState() : Trigger.TriggerState.NONE;
    }

    @Override
    public void pauseTrigger(TriggerKey triggerKey) {
        updateTriggerState(triggerKey, Trigger.TriggerState.PAUSED);
    }

    @Override
    public Set<String> pauseTriggers(GroupMatcher<TriggerKey> matcher) {
        Set<TriggerKey> keys = getTriggerKeys(matcher);
        keys.forEach(this::pauseTrigger);
        return keys.stream().map(TriggerKey::getGroup).collect(Collectors.toSet());
    }

    @Override
    public void pauseJob(JobKey jobKey) {
        getTriggersForJob(jobKey).forEach(trigger -> pauseTrigger(trigger.getKey()));
    }

    @Override
    public Set<String> pauseJobs(GroupMatcher<JobKey> matcher) {
        Set<JobKey> keys = getJobKeys(matcher);
        keys.forEach(this::pauseJob);
        return keys.stream().map(JobKey::getGroup).collect(Collectors.toSet());
    }

    @Override
    public void resumeTrigger(TriggerKey triggerKey) {
        updateTriggerState(triggerKey, Trigger.TriggerState.NORMAL);
    }

    @Override
    public Set<String> resumeTriggers(GroupMatcher<TriggerKey> matcher) {
        Set<TriggerKey> keys = getTriggerKeys(matcher);
        keys.forEach(this::resumeTrigger);
        return keys.stream().map(TriggerKey::getGroup).collect(Collectors.toSet());
    }

    @Override
    public void resumeJob(JobKey jobKey) {
        getTriggersForJob(jobKey).forEach(trigger -> resumeTrigger(trigger.getKey()));
    }

    @Override
    public Set<String> resumeJobs(GroupMatcher<JobKey> matcher) {
        Set<JobKey> keys = getJobKeys(matcher);
        keys.forEach(this::resumeJob);
        return keys.stream().map(JobKey::getGroup).collect(Collectors.toSet());
    }

    @Override
    public void pauseAll() {
        getTriggerGroupNames().forEach(group -> pauseTriggers(GroupMatcher.triggerGroupEquals(group)));
    }

    @Override
    public void resumeAll() {
        getTriggerGroupNames().forEach(group -> resumeTriggers(GroupMatcher.triggerGroupEquals(group)));
    }

    @Override
    public List<OperableTrigger> acquireNextTriggers(long noLaterThan, int maxCount, long timeWindow) {
        List<OperableTrigger> acquiredTriggers = new ArrayList<>();
        long limit = noLaterThan + timeWindow;

        // 循环以原子方式获取最多"maxCount"个触发器。
        for (int i = 0; i < maxCount; i++) {
            // 定义查询条件，查找可触发的触发器（在正确的时间，处于正确的状态）。
            Criteria criteria = new Criteria().andOperator(
                    Criteria.where("nextFireTime").lte(new Date(limit)),
                    new Criteria().orOperator(
                            Criteria.where("triggerState").is(Trigger.TriggerState.NORMAL),
                            Criteria.where("triggerState").is(null) // 将 null 视作 NORMAL
                    )
            );
            Query query = new Query(criteria)
                    .with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "nextFireTime"));

            // 定义一个原子性的更新操作，将触发器的状态"锁定"为 BLOCKED
            Update update = new Update().set("triggerState", Trigger.TriggerState.BLOCKED);

            // 执行原子的"查找并修改"操作。
            QuartzTriggerDO acquired = mongoTemplate.findAndModify(query, update, QuartzTriggerDO.class);

            if (acquired == null) {
                // 没有更多符合条件的触发器可以获取。
                break;
            }

            // 我们现在已经获得了触发器的独占锁（状态为 BLOCKED）。
            // 接下来，检查它是否是一次"错过触发 (Misfire)"。
            long misfireTime = System.currentTimeMillis() - acquired.getNextFireTime().getTime();
            if (misfireTime > this.misfireThreshold) {
                // 这是一次真正的 Misfire，执行 Misfire 处理逻辑，而不是直接执行它。
                handleMisfiredTrigger(acquired);
                // 继续循环，尝试获取下一个正常的触发器。
                continue;
            }

            // 'acquired' 对象是更新前的文档。我们现在已经获得了锁。
            // 我们需要将其转换为 OperableTrigger 以便返回。
            OperableTrigger trigger = acquired.getTrigger();
            if (trigger != null) {
                acquiredTriggers.add(trigger);
            }
        }
        return acquiredTriggers;
    }

    @Override
    public void releaseAcquiredTrigger(OperableTrigger trigger) {
        // 当调度器获取了一个触发器但随后决定不触发它时，会调用此方法。
        // 我们必须通过将其状态设置回 NORMAL 来"解锁"它。
        this.updateTriggerState(trigger.getKey(), Trigger.TriggerState.NORMAL);
    }

    @Override
    public List<TriggerFiredResult> triggersFired(List<OperableTrigger> triggers) throws JobPersistenceException {
        List<TriggerFiredResult> results = new ArrayList<>();
        for (OperableTrigger trigger : triggers) {
            JobDetail jobDetail = retrieveJob(trigger.getJobKey());
            if (jobDetail == null) {
                // 此触发器关联的作业不存在，可能是一个孤立的触发器。
                // 我们将忽略它。或者，我们可以记录此事件或删除该触发器。
                continue;
            }
            results.add(new TriggerFiredResult(new TriggerFiredBundle(
                    jobDetail, trigger,
                    retrieveCalendar(trigger.getCalendarName()),
                    false, new Date(), trigger.getPreviousFireTime(), trigger.getNextFireTime(), trigger.getNextFireTime()
            )));
        }
        return results;
    }

    @Override
    public void triggeredJobComplete(OperableTrigger trigger, JobDetail jobDetail, Trigger.CompletedExecutionInstruction instruction) {
        // 处理由 scheduler.triggerJob() 创建的一次性触发器最稳健的方法是
        // 检查 Quartz 用于它们的命名约定（"MT_" 代表手动触发）。
        // 这比检查像 mayFireAgain() 这样的内部状态更可靠，后者已被证明存在问题。
        if (trigger.getKey().getName().startsWith("MT_")) {
            removeTrigger(trigger.getKey());
            return;
        }

        // 对于持久化触发器（例如，cron 作业），我们仍然检查它们是否已完成。
        if (!trigger.mayFireAgain()) {
            removeTrigger(trigger.getKey());
            return;
        }

        // 如果代码执行到这里，说明它是一个将再次触发的重复性触发器。
        // 我们必须保存它的新状态和下一次触发时间。

        // 关键的第一步：告知触发器它刚刚被触发，让它在内存中更新自己的状态（例如，计算下一次触发时间）。
        // 这是 JobStore 的核心职责之一。
        org.quartz.Calendar cal = null;
        if (trigger.getCalendarName() != null) {
            cal = retrieveCalendar(trigger.getCalendarName());
        }
        trigger.triggered(cal);

        // 为了避免由于对象映射和 save 方法的默认行为（例如忽略 null 值）导致的问题，
        // 我们直接构建一个 Update 对象来精确地更新所需的字段。
        Query query = triggerQuery(trigger.getKey());
        Update update = new Update();

        // 1. 强制更新下一次和上一次的触发时间
        update.set("nextFireTime", trigger.getNextFireTime());
        update.set("prevFireTime", trigger.getPreviousFireTime());

        // 2. 将状态从 BLOCKED 重置回 NORMAL 或 PAUSED
        //    我们需要先读一下当前状态来判断是否是 PAUSED，以防在任务执行期间，该触发器被外部操作暂停了。
        QuartzTriggerDO existingTrigger = findTrigger(trigger.getKey());
        if (existingTrigger != null && existingTrigger.getTriggerState() == Trigger.TriggerState.PAUSED) {
            update.set("triggerState", Trigger.TriggerState.PAUSED);
        } else {
            update.set("triggerState", Trigger.TriggerState.NORMAL);
        }

        // 3. 更新 JobDataMap（如果它在执行期间被修改了）
        //    @PersistJobDataAfterExecution 注解要求我们这样做
        if (jobDetail.isPersistJobDataAfterExecution()) {
            update.set("jobDataMap", jobDetail.getJobDataMap());
        }

        mongoTemplate.updateFirst(query, update, QuartzTriggerDO.class);
    }

    @Override
    public void storeCalendar(String name, org.quartz.Calendar calendar, boolean replaceExisting, boolean updateTriggers) throws ObjectAlreadyExistsException {
        if (!StringUtils.hasText(name)) {
            throw new IllegalArgumentException("Calendar name cannot be empty.");
        }
        if (mongoTemplate.exists(new Query(Criteria.where("name").is(name)), QuartzCalendarDO.class) && !replaceExisting) {
            throw new ObjectAlreadyExistsException("Calendar with name '" + name + "' already exists.");
        }
        mongoTemplate.save(new QuartzCalendarDO(name, calendar));
    }

    @Override
    public boolean removeCalendar(String calName) {
        return mongoTemplate.remove(new Query(Criteria.where("name").is(calName)), QuartzCalendarDO.class).getDeletedCount() > 0;
    }

    @Override
    public org.quartz.Calendar retrieveCalendar(String calName) {
        QuartzCalendarDO doc = mongoTemplate.findOne(new Query(Criteria.where("name").is(calName)), QuartzCalendarDO.class);
        return doc != null ? doc.getCalendar() : null;
    }

    @Override
    public int getNumberOfJobs() {
        return (int) mongoTemplate.count(new Query(), QuartzJobDO.class);
    }

    @Override
    public int getNumberOfTriggers() {
        return (int) mongoTemplate.count(new Query(), QuartzTriggerDO.class);
    }

    @Override
    public int getNumberOfCalendars() {
        return (int) mongoTemplate.count(new Query(), QuartzCalendarDO.class);
    }

    @Override
    public Set<String> getPausedTriggerGroups() {
        return mongoTemplate.find(new Query(Criteria.where("triggerState").is(Trigger.TriggerState.PAUSED)), QuartzTriggerDO.class)
                .stream()
                .map(doc -> doc.getTriggerKey().getGroup())
                .collect(Collectors.toSet());
    }

    @Override
    public List<String> getCalendarNames() throws JobPersistenceException {
        return mongoTemplate.query(QuartzCalendarDO.class).distinct("name").as(String.class).all();
    }

    @Override
    public long getAcquireRetryDelay(int failureCount) {
        return 1000L * failureCount;
    }

    @Override
    public void shutdown() {
        // 未实现
    }

    @Override
    public void resetTriggerFromErrorState(TriggerKey triggerKey) {
        updateTriggerState(triggerKey, Trigger.TriggerState.NORMAL);
    }

    private Query jobQuery(JobKey jobKey) {
        return new Query(Criteria.where("jobGroup").is(jobKey.getGroup()).and("jobName").is(jobKey.getName()));
    }

    private Query triggerQuery(TriggerKey triggerKey) {
        return new Query(Criteria.where("triggerGroup").is(triggerKey.getGroup()).and("triggerName").is(triggerKey.getName()));
    }

    private Criteria createCriteria(GroupMatcher<?> matcher, String groupField) {
        String value = matcher.getCompareToValue();
        if ("*".equals(value)) {
            return new Criteria();
        }

        switch (matcher.getCompareWithOperator()) {
            case EQUALS:
                return Criteria.where(groupField).is(value);
            case STARTS_WITH:
                return Criteria.where(groupField).regex("^" + value);
            case ENDS_WITH:
                return Criteria.where(groupField).regex(value + "$");
            case CONTAINS:
                return Criteria.where(groupField).regex(value);
            default:
                throw new UnsupportedOperationException("Unsupported GroupMatcher operator: " + matcher.getCompareWithOperator());
        }
    }

    private void updateTriggerState(TriggerKey triggerKey, Trigger.TriggerState state) {
        QuartzTriggerDO trigger = findTrigger(triggerKey);
        if (trigger != null) {
            trigger.setTriggerState(state);
            mongoTemplate.save(trigger);
        }
    }

    private QuartzJobDO findJob(JobKey jobKey) {
        return mongoTemplate.findOne(jobQuery(jobKey), QuartzJobDO.class);
    }

    private QuartzTriggerDO findTrigger(TriggerKey triggerKey) {
        return mongoTemplate.findOne(triggerQuery(triggerKey), QuartzTriggerDO.class);
    }

    public void setMisfireThreshold(long misfireThreshold) {
        this.misfireThreshold = misfireThreshold;
    }

    public void setIsClustered(boolean isClustered) {
        this.isClustered = isClustered;
    }

    public void setClusterCheckinInterval(long clusterCheckinInterval) {
        this.clusterCheckinInterval = clusterCheckinInterval;
    }

    /**
     * 处理错过触发的触发器（Misfired Trigger）。
     * 根据我们的策略，我们将记录一个"跳过"的日志，然后更新触发器到其下一次正常的触发时间。
     *
     * @param misfiredTriggerDoc 错过的触发器文档对象
     */
    private void handleMisfiredTrigger(QuartzTriggerDO misfiredTriggerDoc) {
        try {
            OperableTrigger trigger = misfiredTriggerDoc.getTrigger();
            if (trigger == null) {
                // 如果无法重建触发器，则直接删除，防止循环
                removeTrigger(misfiredTriggerDoc.getTriggerKey());
                return;
            }

            // 1. 核心修复：调用 updateAfterMisfire 来智能地处理 Misfire。
            //    这个方法会根据触发器自身的 Misfire 指令，将 nextFireTime 更新到一个正确的未来时间点。
            org.quartz.Calendar cal = null;
            if (trigger.getCalendarName() != null) {
                cal = retrieveCalendar(trigger.getCalendarName());
            }
            trigger.updateAfterMisfire(cal);

            // 2. 更新数据库中的触发器
            //    如果 updateAfterMisfire 计算后，触发器不再会触发（getNextFireTime 为 null），
            //    我们就应该删除它，而不是更新。
            if (trigger.getNextFireTime() != null) {
                Update update = new Update()
                        .set("nextFireTime", trigger.getNextFireTime())
                        .set("triggerState", Trigger.TriggerState.NORMAL); // 释放锁
                mongoTemplate.updateFirst(triggerQuery(trigger.getKey()), update, QuartzTriggerDO.class);
            } else {
                removeTrigger(trigger.getKey());
            }

            // 3. 记录一个"错过并跳过"的日志
            JobDetail jobDetail = retrieveJob(trigger.getJobKey());
            if (jobDetail != null && jobLogFrameworkService != null) {
                String jobId = jobDetail.getJobDataMap().getString(JobDataKeyEnum.JOB_ID.name());
                String jobHandlerName = jobDetail.getJobDataMap().getString(JobDataKeyEnum.JOB_HANDLER_NAME.name());
                String jobHandlerParam = jobDetail.getJobDataMap().getString(JobDataKeyEnum.JOB_HANDLER_PARAM.name());

                LocalDateTime nowDateTime = LocalDateTime.now();
                // 将 Date 转换为 Instant，其 toString() 方法默认输出 ISO 8601 格式 (例如：2025-06-10T09:22:39.608Z)
                String reason = "任务misfire被跳过, 原始触发时间: " + misfiredTriggerDoc.getNextFireTime().toInstant();
                // 一步到位地创建一条已完成的日志
                jobLogFrameworkService.createCompletedJobLog(jobId, nowDateTime, nowDateTime, jobHandlerName,
                        jobHandlerParam, 0, 0, false, reason);
            }
        } catch (Exception e) {
            org.slf4j.LoggerFactory.getLogger(MongoJobStore.class)
                    .error("处理错过触发的触发器失败: {}", misfiredTriggerDoc.getTriggerKey(), e);
            // 如果处理失败，为了安全起见，直接删除该触发器，防止无限循环。
            removeTrigger(misfiredTriggerDoc.getTriggerKey());
        }
    }
}