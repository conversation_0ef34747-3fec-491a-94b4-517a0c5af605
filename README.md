# MDIS

## Master Data Integration System 主数据集成系统

基础框架基于 ruoyi-vue-pro 修改, 数据库使用 MongoDB

前端 Vue + Element Plus, 后端 Spring Boot, 数据库 MongoDB

## redis 8-alpine

```bash  
docker run -itd --name redis-demo -p 6379:6379 redis
```

module, mdis-server的application-local.yaml文件中, 配置redis, 更改database为1;

```yml
redis:  
  host: 127.0.0.1 # 地址  
  port: 6379 # 端口  
  database: 3 # 3-rapid-local,2-local,1-stage,0-prod
```

## Maven Compile

get into project directory

```bash  
mvn clean install '-Dmaven.test.skip=true'
```

## 打开bpm, report, mall, pay, mp (可选)

- 根pom.xml, 打开相关modules
- mdis-server/pom.xml, 打开相关dependency
- 导入相关sql文件 workbench/mdis/sql

## application-local.yaml

- 打开了,spring.autoconfigure.exclude: org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration # 默认 local 环境，不开启 Quartz 的自动配置

## jar包输出

release, 和 hotfix 分支专用于发 jar 包; release 发布大版本, hotfix 发布临时修改;

其他阶段性分支比如(user-stage-jdk21), 定期合并到 master 分支;

master 分支是jdk21版本;

jar 创建

```shell
mvn clean install '-Dmaven.test.skip=true'
rsync -avz /Users/<USER>/workbench/rapid/rapid-boot/mdis-server/target/mdis-server.jar debian:/root/rapid-docker/
```

## 应用 BigDecimal 精度控制的描述

为了解决 BigDecimal 在前后端传输过程中精度丢失的问题（如 `8.109232566263768` 变成 `8.109232566263769`），项目采用了统一的精度控制方案。

### 1. 统一精度定义

**文件位置**: `mdis-framework/mdis-common/src/main/java/net/iofun/mdis/framework/common/util/number/NumberUtils.java`
**配置内容**: 定义 `DECIMAL_PRECISION = MathContext.DECIMAL64`
**目的**: 为整个项目提供统一的 BigDecimal 精度控制标准

### 2. MongoDB 数据库层精度控制

**文件位置**: `mdis-framework/mdis-common/src/main/java/net/iofun/mdis/framework/common/convert/BigDecimalToDecimal128Converter.java`
**配置内容**: 在写入 MongoDB 时，使用统一精度进行四舍五入后再转换为 Decimal128
**目的**: 确保存储到数据库的数据使用统一精度

**文件位置**: `mdis-framework/mdis-common/src/main/java/net/iofun/mdis/framework/common/convert/Decimal128ToBigDecimalConverter.java`
**配置内容**: 在读取 MongoDB 时，将 Decimal128 转换为 BigDecimal 后使用统一精度进行四舍五入
**目的**: 确保从数据库读取的数据使用统一精度

### 3. HTTP 传输层精度控制

**文件位置**: `mdis-framework/mdis-common/src/main/java/net/iofun/mdis/framework/common/util/json/databind/BigDecimalSerializer.java`
**配置内容**: 自定义 Jackson 序列化器，将 BigDecimal 使用统一精度四舍五入后序列化为字符串
**目的**: 避免 JSON 序列化过程中的浮点数精度丢失

**文件位置**: `mdis-framework/mdis-common/src/main/java/net/iofun/mdis/framework/common/util/json/databind/BigDecimalDeserializer.java`
**配置内容**: 自定义 Jackson 反序列化器，将 JSON 字符串反序列化为使用统一精度的 BigDecimal
**目的**: 确保反序列化时使用统一精度

### 4. Jackson 配置注册

**文件位置**: `mdis-framework/mdis-spring-boot-starter-web/src/main/java/net/iofun/mdis/framework/jackson/config/MdisJacksonAutoConfiguration.java`
**配置内容**: 在 SimpleModule 中注册 BigDecimal 的序列化器和反序列化器
**目的**: 使自定义的 BigDecimal 序列化器在整个应用中生效

### 5. MongoDB 转换器注册

**文件位置**: `mdis-framework/mdis-spring-boot-starter-mongo/src/main/java/net/iofun/mdis/framework/mongo/config/MdisDefaultMongoConfiguration.java`
**配置内容**: 在 MongoCustomConversions 中注册 BigDecimal 与 Decimal128 的转换器
**目的**: 使自定义的 MongoDB 转换器在整个应用中生效

### 效果

通过以上配置，确保了 BigDecimal 数据在以下环节都使用统一精度：

- 内存中的 BigDecimal 运算
- MongoDB 数据库存储和读取
- 前后端 JSON 数据传输
- AviatorScript 表达式计算

最终解决了前后端 BigDecimal 精度不一致的问题。

## 后端应用 AviatorScript 表达式计算的描述

项目集成了 AviatorScript 表达式计算引擎，用于处理产品属性中的复杂计算公式，支持高精度数值计算。

### 1. AviatorScript 模块配置

**模块路径**: `mdis-framework/mdis-spring-boot-starter-aviatorscript/`

**关键配置文件**:

- `src/main/java/net/iofun/mdis/framework/aviatorscript/config/AviatorScriptProperties.java` - 配置属性类，定义精度、缓存、沙盒等参数
- `src/main/java/net/iofun/mdis/framework/aviatorscript/config/AviatorScriptAutoConfiguration.java` - 自动配置类，注册 ExpressionCalculator Bean
- `src/main/java/net/iofun/mdis/framework/aviatorscript/core/ExpressionCalculator.java` - 核心计算器，集成 AviatorScript 引擎
- `src/main/resources/META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports` - Spring Boot 自动配置导入

**其他目录作用**:

- `src/main/java/net/iofun/mdis/framework/aviatorscript/model/` - 数据模型类（ExpressionRequest、ExpressionVariable、ComputationResult）
- `src/main/java/net/iofun/mdis/framework/aviatorscript/function/math/` - 自定义数学函数（如 MathPiFunction）
- `src/test/` - 单元测试和集成测试

### 2. Product 模块中的使用

**控制器路径**: `mdis-module-product/src/main/java/net/iofun/mdis/module/product/controller/admin/computation/ComputationController.java`

**提供的接口**:

- `POST /product/computation/validate-expression` - 验证表达式合法性
- `POST /product/computation/compute-by-value` - 根据变量值直接计算
- `POST /product/computation/compute-by-refer` - 根据变量引用解析后计算

**服务实现**: `mdis-module-product/src/main/java/net/iofun/mdis/module/product/service/computation/ComputationServiceImpl.java`

## 后端应用 QLExpress 嵌入式Java动态脚本工具



## 前端实现

### 1. Decimal 精度配置

**文件路径**: `/Users/<USER>/workbench/mdis/mdis-web/src/views/product/utils/decimal-config.ts`

**配置内容**:  

- 使用 `decimal.js` 库，设置 `precision: 16` 匹配后端 `MathContext.DECIMAL64`
- 配置舍入模式、指数阈值等参数，确保前后端精度一致
- 提供 `DecimalUtils` 工具类，包含格式化、转换等常用方法

### 2. 表达式构建工具

**目录路径**: `/Users/<USER>/workbench/mdis/mdis-web/src/views/product/property/component/expression/`

**核心文件**:

- `expressionCalculator.ts` - 表达式计算工具，包含变量验证、赋值转换等功能
- `ExpressionForm.vue` - 表达式表单组件，提供可视化的表达式编辑界面
- `expression.ts` - 表达式相关的类型定义和工具函数

**主要功能**:

- 验证表达式中的变量引用是否在属性列表中存在
- 为表达式变量赋值，转换为 AviatorScript 格式
- 支持变量引用解析和直接值计算两种模式

### 3. 计算接口调用

**文件路径**: `/Users/<USER>/workbench/mdis/mdis-web/src/api/product/computation.ts`

**接口定义**:

- `validateExpression` - 验证表达式
- `calculateDirectly` - 直接计算
- `calculateByValue` - 通过变量值计算
- `calculateByRefer` - 通过引用计算

**数据类型**:

- `PreciseNumber` - 高精度数值类型（string | number | Decimal）
- `ComputationVarVO` - 计算变量 VO
- `ComputationReqVO` - 计算请求 VO

### 精度一致性保证

1. **后端**: 使用 `NumberUtils.DECIMAL_PRECISION = MathContext.DECIMAL64` 统一精度
2. **前端**: 使用 `decimal.js` 配置 `precision: 16` 匹配后端精度
3. **AviatorScript**: 配置 `Options.MATH_CONTEXT` 使用统一精度
4. **数据传输**: 通过字符串格式传输，避免 JSON 序列化精度丢失

### 使用场景

1. **产品属性计算**: 在 ProductSku 创建时验证计算属性的表达式和结果
2. **实时计算**: 前端编辑表达式时实时验证和计算
3. **批量计算**: 支持批量处理多个表达式的计算需求
4. **变量解析**: 支持通过 valueId、spuId、skuId 等引用解析变量值
